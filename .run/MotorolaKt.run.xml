<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="MotorolaKt" type="MicronautRunConfigurationType" factoryName="Micronaut">
    <option name="ALTERNATIVE_JRE_PATH" value="17" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="ai.friday.dda.ApplicationKt" />
    <module name="dda-service" />
    <option name="VM_PARAMETERS" value="-Dmicronaut.environments=prodmotorola,motorola,friday" />
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" value="profile:motorola-prod" />
      <option name="region" value="us-east-1" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <option name="alternativeJrePath" value="17" />
    <option name="alternativeJrePathEnabled" value="true" />
    <option name="mainClass" value="ai.friday.dda.ApplicationKt" />
    <option name="passParentEnvs" value="true" />
    <option name="programParameters" value="" />
    <option name="vmParameters" value="-Dmicronaut.environments=prodmotorola,motorola,friday" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>