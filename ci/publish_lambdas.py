import argparse
import json
import sys
import boto3
import time

parser = argparse.ArgumentParser()

parser.add_argument('-o', '--output')
parser.add_argument('-b', '--s3-bucket')
parser.add_argument('-l', '--lambdas', action='append')
parser.add_argument('-y', '--layers', action='append', default=[])

args = parser.parse_args()

output_file = args.output
s3_bucket_name = args.s3_bucket
s3_keys_by_function_name = {}
s3_layers = {}

for entry in args.lambdas:
    parts = entry.split(' ')
    s3_keys_by_function_name[parts[0]] = parts[1]

for entry in args.layers:
    parts = entry.split(' ')
    s3_layers[parts[0]] = parts[1]

print('Running with parameters...')
print('output_file: %s' % output_file)
print('s3_bucket_name: %s' % s3_bucket_name)
print('s3_layers: %s' % json.dumps(s3_layers))
print('s3_keys_by_function_name: %s' % json.dumps(s3_keys_by_function_name))

def wait_for_update_completion(function_name):
    """
    Aguarda a conclusão de uma atualização da função Lambda.
    """
    while True:
        response = lambda_client.get_function_configuration(FunctionName=function_name)
        status = response.get('LastUpdateStatus')
        if status == 'Successful':
            break
        elif status == 'Failed':
            raise Exception(f"Atualização da função {function_name} falhou.")
        print(f"Aguardando a conclusão da atualização para {function_name}... Status atual: {status}")
        time.sleep(5)


def try_update_function_code(s3_bucket_name, function_name, s3_key, s3_layers):
    response_layer_arns = {}
    for current_layer_name in s3_layers:
        response_layer = lambda_client.publish_layer_version(
            LayerName=current_layer_name,
            Content={
                'S3Bucket': s3_bucket_name,
                'S3Key': s3_layers[current_layer_name]
            },
            CompatibleRuntimes=['Python 3.9']
        )
        # Obter o ARN da nova versão do layer
        new_layer_arn = response_layer['LayerVersionArn']
        print(f"Novo layer publicado: {new_layer_arn}")
        response_layer_arns[current_layer_name] = new_layer_arn


    for current_layer_name in response_layer_arns:
        response_config = lambda_client.update_function_configuration(
            FunctionName=function_name,
            Layers=[response_layer_arns[current_layer_name]]
        )
        print("Configuração da função atualizada com o novo layer:", response_layer_arns[current_layer_name])
        wait_for_update_completion(function_name)

    print('Updating function code...')
    return lambda_client.update_function_code(
        FunctionName=function_name,
        S3Bucket=s3_bucket_name,
        S3Key=s3_key,
        Publish=True)['FunctionArn']


print('Setting up lambda client...')
lambda_client = boto3.client('lambda')

print('Attempting to publish lambdas...')
arns_by_function_name = {}

for function_name in s3_keys_by_function_name:
    print('With lambda %s...' % (function_name))
    arns_by_function_name[function_name] = try_update_function_code(
        s3_bucket_name,
        function_name,
        s3_keys_by_function_name[function_name],
        s3_layers
    )

print('ARNs by function name:')
print(json.dumps(arns_by_function_name, indent=4))

with open(output_file, 'w') as file:
    json.dump(arns_by_function_name, file)

print('Result written to %s' % (output_file))
print('Lambda publishing is done.')
exit(0)
