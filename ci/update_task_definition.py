import argparse
import json
import sys

parser = argparse.ArgumentParser('Replaces image in the task definition')
parser.add_argument('image_uri', metavar='I', type=str, nargs='+',
                   help='The new image URI')
parser.add_argument('app_version', metavar='V', type=str, nargs='+',
                   help='Application Version')

args = parser.parse_args()

definition = json.load(sys.stdin)['taskDefinition']
containerDefinition = definition['containerDefinitions'][0]

containerDefinition['image'] = args.image_uri[0] + ":" + args.app_version[0]

# updates DD_VERSION if present
for entry in containerDefinition['environment']:
    if entry['name'] == 'DD_VERSION':
        entry['value'] = args.app_version[0]

# updates com.datadoghq.tags.version if present
if 'com.datadoghq.tags.version' in containerDefinition['dockerLabels']:
    containerDefinition['dockerLabels']['com.datadoghq.tags.version'] = args.app_version[0]

del definition['taskDefinitionArn']
del definition['revision']
del definition['status']
del definition['requiresAttributes']
del definition['compatibilities']
del definition['registeredAt']
del definition['registeredBy']

print(json.dumps(definition))