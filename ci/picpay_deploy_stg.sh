#!/bin/bash

cd ..

REGION=sa-east-1

CLUSTER_NAME=bill-payment-cluster

APP_NAME=dda-bills
SERVICE_NAME=dda-bills-service
TASK_DEFINITION_NAME=dda-bills-task
AWS_ACCOUNT_ID=************
#AWS_ACCOUNT_ID=************
IMAGE_NAME=dda-bills
AWS_ECR=${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com
AWS_ECR_REPOSITORY=${AWS_ECR}/${IMAGE_NAME}


main() {
  TS=$(date +%s)
  VERSION=test-qa-$TS

  echo deploy start: version["$VERSION"]

  mvn_build
  ecr_login
  image_build_and_push "$VERSION"
  ecs_new_task_definition "$VERSION"
  ecs_update_service

  echo deploy finish: version["$VERSION"]
}

function mvn_build() {
  ./mvnw antrun:run@ktlint-format

  ./mvnw clean verify -Dmaven.final-name-jar=app -DskipTests
}

function ecr_login() {
  aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $AWS_ECR
}

function image_build_and_push() {
  local VERSION=$1

  docker build --platform=linux/amd64 --build-arg APP_VERSION="$VERSION" -t $IMAGE_NAME:"$VERSION" .
  docker tag $IMAGE_NAME:"$VERSION" $AWS_ECR_REPOSITORY:"$VERSION"
  docker push $AWS_ECR_REPOSITORY:"$VERSION"
}

function ecs_new_task_definition() {
  local VERSION=$1

  TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME")
  NEW_CONTAINER_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg NEW_IMAGE "$AWS_ECR_REPOSITORY:$VERSION" '.taskDefinition.containerDefinitions[0].image |= $NEW_IMAGE' | jq '.taskDefinition.containerDefinitions')
  VOLUMES=$(echo "$TASK_DEFINITION" | jq -c '.taskDefinition.volumes')

  CURRENT_CPU=$(echo "$TASK_DEFINITION" | jq '.taskDefinition.cpu|tonumber')
  CURRENT_MEMORY=$(echo "$TASK_DEFINITION" | jq '.taskDefinition.memory|tonumber')

  echo "==== ECS - New Task Definition ===="
  aws ecs register-task-definition \
    --family "$TASK_DEFINITION_NAME" \
    --container-definitions "$NEW_CONTAINER_DEFINITION" \
    --requires-compatibilities "FARGATE" \
    --network-mode awsvpc \
    --cpu "$CURRENT_CPU" \
    --memory "$CURRENT_MEMORY" \
    --volumes "$VOLUMES" \
    --task-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/$APP_NAME-task-role \
    --execution-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/$APP_NAME-task-exec-role >/dev/null 2>&1
}

function ecs_update_service() {
  ECS_SERVICE=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME)

  CURRENT_DIRED_COUNT=$(echo "$ECS_SERVICE" | jq ".services[] | select( .serviceName = \"${APP_NAME}\") | .desiredCount")

  echo "==== ECS - Update Service ===="
  aws ecs update-service \
    --cluster "$CLUSTER_NAME" \
    --service "$SERVICE_NAME" \
    --task-definition "$TASK_DEFINITION_NAME" \
    --desired-count "$CURRENT_DIRED_COUNT" >/dev/null 2>&1
}

main