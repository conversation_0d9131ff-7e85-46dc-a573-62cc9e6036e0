# DDA Service

## Intel vs ARM Based Macs

DynamoDB Local (used for local tests) uses SQLite4java which in turn uses a binary SQLite library. In order to
run local tests on MacOS you need to symlink the library that suits your CPU architecture

### Intel Based Macs
> ln -fs libsqlite4java-osx-intel-1.0.392.dylib ./native-libs/libsqlite4java-osx-1.0.392.dylib

### ARM Based Macs
> ln -fs libsqlite4java-osx-arm64-1.0.392.dylib ./native-libs/libsqlite4java-osx-1.0.392.dylib

## Deploy Picpay Produção

- Gerar um PIPELINE_TRIGGER_TOKEN (só precisa gerar 1 por usuário - Settings > CI/CD > Pipeline triggers)
- Exportar credenciais da AWS Picpay como variavel de ambiente
- Gerar uma tag no gitlab
- Rodar o seguinte comando:

```
export LAST_GIT_TAG=$(git describe --tags --abbrev=0)
curl -v --request POST \
   --form token="PIPELINE_TRIGGER_TOKEN" \
   --form ref="${LAST_GIT_TAG}" \
   --form "variables[CI_ENVIRONMENT_NAME]=picpay" \
   --form "variables[AWS_ACCESS_KEY_ID]=${AWS_ACCESS_KEY_ID}" \
   --form "variables[AWS_SECRET_ACCESS_KEY]=${AWS_SECRET_ACCESS_KEY}" \
   --form "variables[AWS_SESSION_TOKEN]=${AWS_SESSION_TOKEN}" \
   "https://gitlab.com/api/v4/projects/36834288/trigger/pipeline"
```

## Jobs

ProcessDDAFilesJob -> Lê arquivos do S3 com os títulos a chegar via DDA. Publica os títulos na fila dda-bills. Roda 2
vezes por hora. Minutos 20 e 50.

## Publicadores de mensagens

|**Fila**   |  **Mensagem** |
|---|---|
| dda-bills  | DDABillsTO |
 
