import ftplib
import logging
import os
import sys
import json
from typing import List
from datetime import datetime
import boto3
from botocore.exceptions import ClientError

today = datetime.today().date()

class FTPConfig:
    def __init__(self, host, port, username, password):
        self.host = host
        self.port = port
        self.username = username
        self.password = password


class FTP_TLS_reuse_session(ftplib.FTP_TLS):
    """Explicit FTPS, with shared TLS session"""

    def ntransfercmd(self, cmd, rest=None):
        conn, size = ftplib.FTP.ntransfercmd(self, cmd, rest)
        if self._prot_p:
            conn = self.context.wrap_socket(conn,
                                            server_hostname=self.host,
                                            session=self.sock.session)  # this is the fix
        return conn, size


def upload_file_to_s3(bucket, key, file_name):
    """Upload a file to an S3 bucket

    :param file_name: File to upload
    :param bucket: Bucket to upload to
    :param key: S3 object name. If not specified then file_name is used
    :param key: file_name stored file name.
    :return: True if file was uploaded, else False
    """

    # Upload the file
    try:
        sys.stdout.write('Enviando arquivo %s para o S3 com a chave %s \n' % (file_name, key))
        s3_client = boto3.client('s3')
        s3_client.upload_file(file_name, bucket, key)
    except ClientError as e:
        logging.error(e)
        return False
    return True


def open_ftps_connection(conf: FTPConfig) -> ftplib.FTP_TLS:
    """
    :rtype: ftplib.FTP_TLS
    """
    ftplib.FTP_TLS.port = conf.port
    ftps = FTP_TLS_reuse_session(conf.host)
    ftps.set_debuglevel(1)
    ftps.login(user=conf.username, passwd=conf.password)
    ftps.prot_p()
    ftps.set_pasv(True)
    return ftps


def retrieve_s3_processed_files(bucket, processed_file_prefix) -> List[str]:
    print('[BEGIN] Listando arquivos do S3: %s' % processed_file_prefix)
    filtered_s3_objects = list(bucket.objects.filter(Prefix=processed_file_prefix))
    filtered_s3_objects_keys = [s3_object.key for s3_object in filtered_s3_objects]
    print(f"[END] Listando arquivos do S3: {processed_file_prefix}. Length: {len(filtered_s3_objects_keys)}")

    return filtered_s3_objects_keys


def retrieve_file_names(ftp_connection: ftplib.FTP_TLS, base_path: str, bucket_name: str, processed_files: List[str], processed_file_prefix: str):
    filtered_s3_objects_keys = processed_files

    paths = [base_path, f"{base_path}/Processed", f"{base_path}/Processing"]

    for path in paths:
        print(f"[BEGIN] Listando arquivos do FTP: {path}")
        ftp_items = list(ftp_connection.mlsd(path))
        print(f"[END] Listando arquivos do FTP: {path}. Length: {len(ftp_items)}")

        for name, facts in ftp_items:
            if facts['type'] != 'dir':
                modified_date = datetime.strptime(facts['modify'][0:14], "%Y%m%d%H%M%S")

                s3_file_path = processed_file_prefix + '/' + name

                if modified_date.date() == today and s3_file_path not in filtered_s3_objects_keys:
                    download_file(ftp_connection, path, name, bucket_name)


def download_file(ftp_connection: ftplib.FTP_TLS, path: str, name: str, bucket_name: str):
    print('[BEGIN] Baixando arquivo: %s/%s \n' % (path, name))
    remote_file_path = path + "/" + name

    file_name = "/tmp/" + name
    handle = open(file_name, 'wb')
    ftp_connection.retrbinary('RETR %s' % remote_file_path, handle.write)
    handle.close()

    upload_file_to_s3(bucket=bucket_name, key='downloaded/' + name, file_name=file_name)
    os.remove(file_name)
    print('[END] Baixando arquivo: %s/%s \n' % (path, name))


def get_secrets(env_prefix=""):
    # Lista das variáveis de ambiente obrigatórias
    prefix = f"{env_prefix}_" if env_prefix else ""
    required_vars = [f'{prefix}ARBI_FTP_HOST', f'{prefix}ARBI_FTP_PORT', f'{prefix}ARBI_FTP_USERNAME', f'{prefix}ARBI_FTP_PASSWORD']

    # Verifica se todas as variáveis estão definidas
    missing_vars = [var for var in required_vars if var not in os.environ]
    if not missing_vars:
        host = os.environ[f'{prefix}ARBI_FTP_HOST']
        port = os.environ[f'{prefix}ARBI_FTP_PORT']
        username = os.environ[f'{prefix}ARBI_FTP_USERNAME']
        password = os.environ[f'{prefix}ARBI_FTP_PASSWORD']

        return FTPConfig(host, port, username, password)

    secret_id = f"{env_prefix}/arbi-ftp" if env_prefix else "CHANGE_ME"
    sm = boto3.client('secretsmanager')
    secret_response = sm.get_secret_value(SecretId=secret_id)

    # Verifica se o valor está em SecretString
    if 'SecretString' in secret_response:
        secret_string = secret_response['SecretString']
    else:
        # Se for um valor binário
        secret_string = secret_response['SecretBinary'].decode('utf-8')

    secrets = json.loads(secret_string)
    return FTPConfig(secrets["host"], secrets["port"], secrets["username"], secrets["password"])


def get_environment_variables(env_prefix=""):
    prefix = f"{env_prefix}_" if env_prefix else ""

    root_folder = os.environ[f'{prefix}FTP_ROOT_FOLDER']
    bucket_name = os.environ[f'{prefix}BUCKET_NAME']

    return root_folder, bucket_name


def get_s3_resources(bucket_name):
    s3_client = boto3.client('s3')
    s3 = boto3.resource('s3')
    bucket = s3.Bucket(bucket_name)

    return s3_client, s3, bucket


def get_processed_file_prefix():
    return 'processed/%s' % today.strftime('%Y%m%d')


def lambda_handler(event, context):
    env_prefix = event.get('environment', '') if isinstance(event, dict) else ''

    root_folder, bucket_name = get_environment_variables(env_prefix)

    s3_client, s3, bucket = get_s3_resources(bucket_name)

    processed_file_prefix = get_processed_file_prefix()

    ftp_config = get_secrets(env_prefix)
    print(f"FTP Host: {ftp_config.host} / Port {ftp_config.port}")

    try:
        ftp_connection = open_ftps_connection(ftp_config)
        processed_files = retrieve_s3_processed_files(bucket, processed_file_prefix)

        retrieve_file_names(ftp_connection, root_folder, bucket_name, processed_files, processed_file_prefix)
    except Exception as e:
        sys.stderr.write(str(e))
