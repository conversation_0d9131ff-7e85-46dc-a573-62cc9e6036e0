FROM adoptopenjdk/openjdk11:jre-11.0.5_10-alpine
#FROM adoptopenjdk/openjdk11:jre-11.0.9.1_1-ubuntu
ARG APP_VERSION
ARG DEFAULT_AGENT_ARGS="-javaagent:dd-java-agent.jar -Ddd.trace.config=dd-java-agent.properties -Ddd.profiling.enabled=true -Ddd.profiling.allocation.enabled=true"

ENV DD_VERSION $APP_VERSION
ENV SERVICE dda-service

ENV AGENT_ARGS=${DEFAULT_AGENT_ARGS}

LABEL com.datadoghq.tags.version="$APP_VERSION"

COPY dd-java-agent.jar dd-java-agent.jar
COPY dd-java-agent.properties dd-java-agent.properties
COPY target/${SERVICE}-*.jar ${SERVICE}.jar
#RUN apt update
#RUN apt -yq install zip
RUN apk update && apk add zip
RUN zip -q -d ${SERVICE}.jar 'META-INF/*.RSA' 'META-INF/*SF' || :

EXPOSE 8443
CMD java -XX:+UseContainerSupport ${AGENT_ARGS} -XX:FlightRecorderOptions=stackdepth=256 -Dcom.sun.management.jmxremote -noverify ${JAVA_OPTS} -jar ${SERVICE}.jar