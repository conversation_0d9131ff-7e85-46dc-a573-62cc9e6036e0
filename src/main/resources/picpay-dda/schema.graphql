scalar Date

type Query {
    # Boleto (Obtém boleto por NumIdentcTit)
    bankSlip(id: ID): BankSlip
    #Lista de boletos
    bankSlips(filter: BankSlipFilter): BankSlipList
}

type BankSlip {
    # NumIdentcTit (Número Identificação Titulo)
    identificationNumber: ID

    # NumCtrlPart (Número de controle Participante)
    participantControlNumber: String

    # ISPBPartRecbdrPrincipal (ISPB Participante Recebedor Principal)
    mainReceivingParticipantISPB: String

    # ISPBPartRecebdrAdmtd (ISPB Participante Recebedor Administrado)
    administeredReceivingParticipantISPB: String

    # NumRefAtlCadTit (Número Referência Atual Cadastro Título)
    registrationCurrentReferenceNumber: String

    # NumSeqAtlzCadTit (Número Sequência Atualização Cadastro Título)
    registrationUpdateSequenceNumber: String

    # ISPBPartDestinatario (ISPB Participante Destinatário)
    recipientParticipantISPB: String

    # CodPartDestinatario (Código Participante Destinatário)
    recipientParticipantCode: String

    # (Beneficiário Original)
    originalBeneficiary: Person

    # (Beneficiário Final)
    finalBeneficiary: Person

    # (Pagador Eletrônico)
    payer: Payer

    # (Sacador Avalista)
    guarantorDrawer: Person

    # CodCartTit (Código Carteira Título)
    portfolioCode: PortfolioCode

    # CodMoedaCNAB (Código Moeda CNAB)
    cnab: CurrencyCode

    # IdentdNossoNum (Identificador Nosso Número)
    ourNumberIdentifier: String

    # NumCodBarras (Número Código de Barras)
    barcodeNumber: String

    # NumLinhaDigtvl (Número Linha Digitável)
    typeableBarcodeNumber: String

    # NumDocTit (Número Documento Título)
    documentNumber: String

    # DtVencTit (Data Vencimento Titulo)
    expirationDate: Date

    # VlrTit (Valor Título)
    amount: Float

    # CodEspTit (Código Espécie Título)
    speciesCode: BankSlipSpeciesCode

    # DtEmsTit (Data Emissão Título)
    dispatchDate: Date

    # QtdDiaPrott (Quantidade Dias Protesto)
    numberOfProtestDays: Int

    # DtLimPgtoTit (Data Limite Pagamento Título)
    paymentDeadline: Date

    # TpPgtoTit (Tipo Pagamento Título)
    paymentType: PaymentType

    # NumParcl (Número Parcela)
    installmentNumber: Int

    # QtdTotParcl (Quantidade Total Parcela)
    numberOfInstallments: Int

    # IndrTitNegcd (Indicador Título Negociado)
    negotiatedIndicator: Boolean

    # IndrBloqPgto (Indicador Bloqueio Pagamento)
    paymentBlockingIndicator: Boolean

    # IndrPgtoParcl (Indicador Pagamento Parcial)
    partialPaymentIndicator: Boolean

    # QtdPgtoParcl (Quantidade Pagamento Parcial)
    partialPaymentAmount: Int

    # VlrAbattTit (Valor Abatimento Título, Utiliza separador decimal "ponto".)
    rebateAmount: Float

    # JurosTit (Juros Título)
    interest: BankSlipInterest

    # MultaTit (Multa Título)
    fine: BankSlipFine

    # RepetDesctTit (Desconto Título)
    discounts: [BankSlipDiscount]

    # RepetNotaFis (Nota Fiscal)
    invoices: [BankSlipInvoice]

    # TpVlrPercMinTit (Tipo Valor ou Percentual Mínimo Título)
    minAmountType: BankSlipAmountType

    # VlrPercMinTit (Valor ou Percentual Mínimo do Título)
    minAmount: Float

    # TpVlrPercMaxTit (Tipo Valor ou Percentual Máximo do Título)
    maxAmountType: BankSlipAmountType

    # VlrPercMaxTit (Valor ou Percentual Máximo do Título)
    maxAmount: Float

    # TpModlCalc (Tipo Modelo Cálculo)
    calculationModel: BankSlipCalculationModel

    # TpAutcRecbtVlrDivgte (Tipo Autorização Recebimento Valor Divergente)
    typeOfAuthorizationForReceiptOfDivergentAmount: AuthorizationType

    # RepetCalcTit (Cálculo Título)
    calculations: [BankSlipCalculation]

    # RepetTxtInf (Texto Informação Beneficiário)
    beneficiaryInformation: [String]

    # RepetActe (Aceite)
    acceptances: [Acceptance]

    # RepetTerc (Terceiro)
    thirdParties: [ThirdParty]

    # SitTit (Situação Título)
    situation: BankSlipSituation

    # DtHrSit (Data Hora Situação)
    updatedAt: String

    # QtdPgtoParclRegtd (Quantidade Pagamento Parcial Registrado)
    partialPaymentAmountRegistered: Int

    # VlrSldTotAtlPgtoTit (Valor Saldo Total Atual Pagamento Título, Utiliza separador decimal "ponto". Ex.: 1599.25)
    currentTotalPaymentAmount: Float

    # SitTitPgto (Situação Título Pagamento)
    paymentSituation: PaymentSituation

    # RepetBaixaOperac (Baixa Operacional)
    operationalWriteOffs: [BankSlipOperationalWriteOff]

    # RepetBaixaEft (Baixa Efetiva)
    effectiveWriteOffs: [EffectiveWriteOff]

    # RepetBaixa (Nova Baixa)
    writeOffs: [BankSlipWriteOff]
}

enum PersonType {
    #Física
    NATURAL

    #Jurídica
    LEGAL
}

type Address {
    # LogradPagdr (Logradouro Pagador) | LogradBenfcrioOr (Logradouro Beneficiário Original)
    address: String

    # CidPagdr (Cidade do Pagador) | CidBenfcrioOr (Cidade do Beneficiário Original)
    city: String

    # UFPagdr (UF Pagador) | UFBenfcrioOr (UF Beneficiário Original)
    state: AddressState

    # CEPPagdr (CEP Pagador) | CEPBenfcrioOr (CEP Beneficiário Original)
    zipCode: String
}

enum AddressState{

    # Amazonas
    AM

    # Alagoas
    AL

    # Acre
    AC

    # Amapá
    AP

    # Bahia
    BA

    # Pará
    PA

    # Mato Grosso
    MT

    # Minas Gerais
    MG

    # Mato Grosso do Sul
    MS

    # Goiás
    GO

    # Maranhão
    MA

    # Rio Grande do Sul
    RS

    # Tocantins
    TO

    # Piauí
    PI

    # São Paulo
    SP

    # Rondônia
    RO

    # Roraima
    RR

    # Paraná
    PR

    # Ceará
    CE

    # Pernambuco
    PE

    # Santa Catarina
    SC

    # Paraíba
    PB

    # Rio Grande do Norte
    RN

    # Espírito Santo
    ES

    # Rio de Janeiro
    RJ

    # Sergipe
    SE

    # Distrito Federal
    DF
}

type Person {
    # TpPessoaBenfcrioOr (Tipo Pessoa Beneficiário Original) | TpPessoaBenfcrioFinl (Tipo Pessoa Beneficiário Final) | TpIdentcSacdrAvalst (Tipo Identificação Sacador Avalista)
    type: PersonType

    # CPFCNPJBenfcrioOr (CNPJ ou CPF Beneficiário Original) | CPFCNPJBenfcrioFinl (CNPJ ou CPF Beneficiário Final) | IdentcSacdrAvalst (Identificação Sacador Avalista)
    document: String

    # NomRzSocBenfcrioOr (Nome ou Razão Social Beneficiário Original) | NomRzSocBenfcrioFinl (Nome ou Razão Social Beneficiário Final) | NomRzSocSacdrAvalst (Nome ou Razão Social Sacador Avalista)
    name: String

    # NomFantsBenfcrioOr (Nome Fantasia Beneficiário Original) | NomFantsBenfcrioFinl (Nome Fantasia Beneficiário Final)
    tradeName: String

    # Endereço
    address: Address
}

enum BankAgencyType {
    # Física
    PHYSICAL

    #Virtual
    VIRTUAL

    #Não Informado
    UNKNOWN
}

enum BankAccountType {
    #Conta Corrente
    CHECKING_ACCOUNT

    #Conta Depósito
    DEPOSIT_ACCOUNT

    #Conta de Pagamento
    PAYMENT_ACCOUNT

    #Poupança
    SAVINGS_ACCOUNT

    #Conta Garantida
    GUARANTEED_ACCOUNT

    #Conta Salário
    SALARY_ACCOUNT

    #Próprio Banco
    OWN_BANK_ACCOUNT
}

type BankAccount {
    # TpAgPagdr (Tipo da Agência do Pagador Eletrônico)
    agencyType: BankAgencyType

    # AgPagdr (Agência do Pagador Eletrônico)
    agency: String

    # TpCtPagdr (Tipo da Conta do Pagador Eletrônico)
    accountType: BankAccountType

    # CtPagdr (Conta do Pagador Eletrônico)
    account: String

    # DtAdesDDA (Data de Adesão no DDA)
    joinedAt: Date
}

type Payer {
    # TpPessoaPagdr (Tipo Pessoa Pagador)
    type: PersonType

    # CPFCNPJPagdr (CNPJ ou CPF Pagador)
    document: String

    # NomRzSocPagdr (Nome ou Razão Social Pagador)
    name: String

    # NomFantsPagdr (Nome Fantasia Pagador)
    tradeName: String

    # (Endereço Pessoa)
    address: Address

    # RepetCtPagdr (Contas do Pagador Eletrônico)
    bankAccounts: [BankAccount]
}

enum PortfolioCode {
    # Cobrança simples
    SIMPLE

    # Cobrança vinculada
    LINKED

    # Cobrança Caucionada
    SECUTIRY

    # Cobrança Descontada
    DEPOSITY

    # Cobrança Vendor
    VENDOR

    # Cobrança Cessão
    ASSIGMENT
}

enum CurrencyCode {
    # Reservado para uso futuro
    RESERVED_FOR_FUTURE

    # Dólar Americano Comercial (Venda)
    COMERCIAL_US_DOLLAR

    # Dólar Americano Turismo (Venda)
    TURISM_US_DOLLAR

    # ITRD
    ITRD

    # IDTR
    IDTR

    # UFIR Diária
    DIARY_UFIR

    # UFIR Mensal
    MONTHLY_UFIR

    # FAJ-TR
    FAJ_TR

    # Real
    REAL

    #TR
    TR

    # IGPM
    IGPM

    # CDI
    CDI

    # Percentual do CDI
    CDI_PERCENTAGE

    # Euro
    EURO
}

enum BankSlipSpeciesCode {
    # CH Cheque
    CH_CHECK

    # DM Duplicata Mercantil
    DM_TRADE_DUPLICATE

    # DMI Duplicata Mercantil Indicação
    DMI_DUPLICATE_MERCANTILE_INDICATION

    # DS Duplicata de Serviço
    DS_DUPLICATE_SERVICE

    # DSI Duplicata de Serviço Indicação
    DSI_DUPLICATE_SERVICE_INDICATION

    # DR Duplicata Rural
    DR_RURAL_DUPLICATE

    # LC Letra de Câmbio
    LC_BILL_OF_EXCHANGE

    # NCC Nota de Crédito Comercial
    NCC_CREDIT_NOTE_COMMERCIAL

    # NCE Nota de Crédito Exportação
    NCE_EXPORT_CREDIT_NOTE

    # NCI Nota de Crédito Industrial
    NCI_INDUSTRIAL_CREDIT_NOTE

    # NCR Nota de Crédito Rural
    NCR_RURAL_CREDIT_NOTE

    # NP Nota Promissória
    NP_PROMISSORY_NOTE

    # NPR Nota Promissória Rural
    NPR_RURAL_PROMISSORY_NOTE

    # TM Triplicata Mercantil
    TM_COMMERCIAL_TRIPLICATE

    # TS Triplicata de Serviço
    TS_TRIPLICATE_SERVICE

    # NS Nota de Seguro
    NS_INSURANCE_NOTE

    # RC Recibo
    RC_RECEIPT

    # FAT Bloqueto
    FAT_BLOCK

    # ND Nota de Débito
    ND_DEBIT_NOTE

    # AP Apólice de Seguro
    AP_INSURANCE_POLICY

    # ME Mensalidade Escolar
    ME_SCHOOL_TUITION

    # PC Parcela de Consórcio
    PC_CONSORTIUM_INSTALLMENT

    # NF Nota Fiscal
    NF_INVOICE

    # DD Documento de Dívida
    DD_DEBT_DOCUMENT

    # Cédula de Produto Rural
    RURAL_PRODUCT_BILL

    # Warrant
    WARRANT

    # Dívida Ativa de Estado
    ACTIVE_STATE_DEBT

    # Dívida Ativa de Município
    MUNICIPAL_ACTIVE_DEBT

    # Dívida Ativa da União
    UNION_ACTIVE_DEBT

    # Encargos condominiais
    CONDOMINIUM_CHARGES

    # Cartão de Crédito
    CREDIT_CARD

    # Boleto proposta
    PROPOSED_BANK_SLIP

    # Boleto de Depósito e Aporte
    DEPOSIT_CONTRIBUTION_BANK_SLIP

    # Outros
    OTHERS
}

enum PaymentType {
    # À vista
    IN_CASH

    # Vencimento determinado
    FIXED_DUE_DATE

    # Carnê
    BOOKLET
}

enum BankSlipInterestCode {
    # Valor (dias corridos)
    VALUE_CURRENT_DAYS

    # Percentual ao dia (dias corridos)
    PERCENTAGE_PER_DAY_CALENDAR_DAYS

    # Percentual ao mês (dias corridos)
    PERCENTAGE_PER_MONTH_CALENDAR_DAYS

    # Percentual ao ano (dias corridos)
    PERCENTAGE_PER_YEAR_CALENDAR_DAYS

    # Isento
    EXEMPT

    # Valor (dia útil)
    VALUE_WORKING_DAY

    # Percentual ao dia (dias úteis)
    PERCENTAGE_PER_DAY_WORKING_DAYS

    # Percentual ao mês (dias úteis)
    PERCENTAGE_PER_MONTH_WORKING_DAYS

    # Percentual ao ano (dias úteis)
    PERCENTAGE_PER_YEAR_WORKING_DAYS
}

type BankSlipInterest {
    # DtJurosTit (Data Juros Título)
    moment: Date

    # CodJurosTit (Código Juros Título)
    code: BankSlipInterestCode

    # VlrPercJurosTit (Valor ou Percentual Juros Título, Utiliza separador decimal "ponto". Ex.: 1599.25355)
    percentage: Float
}

enum BankSlipFineType {
    # 1 (Valor Fixo)
    FIXED_VALUE

    # 2 (Porcentagem)
    PERCENTAGE

    # 3 (Isento)
    EXEMPT
}

type BankSlipFine {
    # DtMultaTit (Data Multa Título)
    moment: Date

    # CodMultaTit (Código Multa Título)
    amountType: BankSlipFineType

    # VlrPercMultaTit (Valor ou Percentual Multa Título, Utiliza separador decimal "ponto". Ex.: 1599.25355)
    amount: Float
}

enum BankSlipDiscountType {
    # Isento
    EXEMPT

    # Valor Fixo até a data informada
    FIXED_AMOUNT_UNTIL_THE_INFORMED_DATE

    # Percentual até a data informada
    PERCENTAGE_UP_TO_THE_DATE_REPORTED

    # Valor por antecipação dia corrido
    AMOUNT_IN_ADVANCE_ON_A_CALENDAR_DAY

    # Valor por antecipação dia útil
    VALUE_IN_ADVANCE_WORKING_DAY

    # Percentual por antecipação dia corrido
    PERCENTAGE_IN_ADVANCE_CALENDAR_DAY

    # Percentual por antecipação dia útil
    PERCENTAGE_IN_ADVANCE_WORKING_DAY
}

type BankSlipDiscount {
    # DtDesctTit (Data Desconto Título)
    moment: String

    # CodDesctTit (Código Desconto Título)
    amountType: BankSlipDiscountType

    # VlrPercDesctTit (Valor ou Percentual Desconto Título, Utiliza separador decimal "ponto". Ex.: 1599.25355)
    amount: Float
}

type BankSlipInvoice {
    # NumNotaFis (Número Nota Fiscal)
    number: String

    # DtEmsNotaFis (Data Emissão Nota Fiscal)
    issuedAt: Date

    # VlrNotaFis (Valor Nota Fiscal, Utiliza separador decimal "ponto". Ex.: 1599.25)
    amount: Float
}

enum BankSlipAmountType {
    # Porcentagem
    PERCENTAGE

    # Valor
    VALUE
}

enum BankSlipCalculationModel {
    # Instituição recebedora calcula títulos a vencer e vencidos
    RECEIVING_INSTITUTION_CALCULATES_EXPIRING_BANK_SLIPS_AND_EXPIRED_BANK_SLIPS

    # Instituição destinatária calcula títulos vencidos e recebedora calcula títulos a vencer
    RECIPIENT_INSTITUTION_CALCULATES_EXPIRED_BANK_SLIPS_AND_RECEIVING_INSTITUTION_CALCULATES_EXPIRING_BANK_SLIPS

    # Instituição destinatária calcula títulos a vencer e vencidos
    RECIPIENT_INSTITUTION_CALCULATES_EXPIRING_BANK_SLIPS_AND_EXPIRED_BANK_SLIPS

    # CIP/Instituição Recebedora calcula saldo para pagamento parcial e títulos a vencer e vencidos em resposta a uma solicitação de consulta
    CIP_RECEIVING_INSTITUTION_CALCULATES_BALANCE_FOR_PARTIAL_PAYMENT_AND_EXPIRING_BANK_SLIPS_AND_EXPIRED_BANK_SLIPS
}

enum AuthorizationType {
    # 1 (Qualquer valor)
    ANY_VALUE

    # 2 (Entre o mínimo e o máximo)
    BETWEEN_MINIMUM_AND_MAXIMUM

    # 3 (Não aceitar pagamento com valor divergente)
    DO_NOT_ACCEPT_PAYMENT_WITH_A_DIFFERENT_AMOUNT

    # 4 (Somente valor mínimo)
    ONLY_MINIMUM_AMOUNT
}

type BankSlipCalculation {
    # VlrCalcdJuros (Valor Calculado Juros, Utiliza separador decimal "ponto". Ex.: 1599.25)
    interestAmount: Float

    # VlrCalcdMulta (Valor Calculado Multa, Utiliza separador decimal "ponto". Ex.: 1599.25)
    fineAmount: Float

    # VlrCalcdDesct (Valor Calculado Desconto, Utiliza separador decimal "ponto". Ex.: 1599.25)
    discountAmount: Float

    # VlrTotCobrar (Valor Total a Cobrar, Utiliza separador decimal "ponto". Ex.: 1599.25)
    totalAmount: Float

    # DtValiddCalc (Data Validade Cálculo)
    dueDate: Date

    # Origem do cálculo
    origin: CalculationOrigin
}

enum CalculationOrigin {
    # Cálculo realizado na PicPay
    PICPAY

    # Cálculo realizado na JD
    JD
}

type Acceptance {
    # NumRefAtlActe (Número Referência Atual Aceite)
    referenceNumber: String

    # NumSeqAtlzActe (Número Sequência Atualização Aceite)
    sequenceNumber: String

    # IndrActe (Indicador Aceite)
    indicator: Boolean
}

enum ThirdPartySituation {
    # Ativo
    ACTIVE

    # Excluído
    EXCLUDED

    # Excluído do Legado com distribuição
    LEGACY_EXCLUDED
}

type ThirdParty {
    # NumIdentcTerc (Número Identificação Terceiro)
    identificationNumber: String

    # NumRefAtlCadTerc (Número Referência Atual Cadastro Terceiro)
    registrationCurrentReferenceNumber: String

    # TpPessoaPagdrAutzdr (Tipo Pessoa Pagador Autorizador)
    authorizerPayerPersonType: PersonType

    # CNPJ_CPFPagdrAutzdr (CNPJ ou CPF Pagador Autorizador)
    authorizerPayerDocument: String

    # TpPessoaTerc (Tipo Pessoa Terceiro)
    personType: PersonType

    # CNPJ_CPFTerc (CNPJ ou CPF Terceiro)
    document: String

    # SitTerc (Situação Terceiro)
    situation: ThirdPartySituation
}

enum BankSlipSituation {
    # Aberto
    OPEN

    # Baixa efetiva por solicitação do Beneficiário
    EFFECTIVE_WRITE_OFF_AT_THE_REQUEST_OF_THE_BENEFICIARY

    # Baixa efetiva por liquidação Intra-bancária
    EFFECTIVE_WRITE_OFF_BY_INTRA_BANK_SETTLEMENT

    # Baixa efetiva por liquidação Interbancária
    EFFECTIVE_WRITE_OFF_BY_INTERBANK_SETTLEMENT

    # Baixa efetiva por decurso de prazo
    EFFECTIVE_WRITE_OFF_DUE_TO_LAPSE_OF_TERM

    # Baixa efetiva por envio para protesto
    EFFECTIVE_WRITE_OFF_BY_SENDING_FOR_PROTEST

    # Baixa efetiva por solicitação da Instituição destinatária
    EFFECTIVE_WRITE_OFF_AT_REQUEST_OF_THE_RECIPIENT_INSTITUTION
}

enum PaymentSituation {
    # Boleto já baixado
    BANK_SLIP_ALREADY_WRITTEN_OFF

    # Boleto bloqueado para pagamento
    BANK_SLIP_BLOCKED_FOR_PAYMENT

    # Boleto encontrado na base centralizada e Cliente Beneficiário inapto na Instituição emissora do título
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_BENEFICIARY_CUSTOMER_UNSUITABLE_AT_THE_INSTITUTION_ISSUING_THE_BANKSLIP

    # Boleto encontrado na base e cliente Beneficiário sem cadastro
    BANK_SLIP_FOUND_IN_THE_BASE_AND_BENEFICIARY_CUSTOMER_WITHOUT_REGISTRATION

    # Boleto encontrado na base centralizada e Cliente Beneficiário em análise na Instituição emissora do título
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_BENEFICIARY_CUSTOMER_UNDER_ANALYSIS_AT_THE_BANK_ISSUING_INSTITUTION

    # Boleto excedeu o limite de pagamentos parciais
    BANK_SLIP_EXCEEDED_THE_LIMIT_FOR_PARTIAL_PAYMENTS

    # Baixa operacional integral já registrada
    FULL_OPERATING_WRITE_OFF_ALREADY_REGISTERED

    # Boleto excedeu o valor de saldo para pagamento parcial para o tipo de modelo de cálculo 04
    BANK_SLIP_EXCEEDED_BALANCE_AMOUNT_FOR_PARTIAL_PAYMENT_FOR_CALCULATION_MODEL_TYPE_FOUR

    # Boleto encontrado na base centralizada e Cliente Beneficiário inapto em Instituição diferente da emissora
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_UNSUITABLE_BENEFICIARY_CUSTOMER_IN_AN_INSTITUTION_OTHER_THAN_THE_ISSUER

    # Boleto encontrado na base centralizada e Cliente Beneficiário em análise em Instituição diferente da emissora
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_BENEFICIARY_CUSTOMER_UNDER_ANALYSIS_AT_AN_INSTITUTION_OTHER_THAN_THE_ISSUER

    # Boleto encontrado na base centralizada e Cliente Beneficiário apto
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_ELIGIBLE_BENEFICIARY_CUSTOMER
}

enum BankSlipWriteOffType {
    # Baixa Operacional Integral Interbancária
    WRITE_OFF_INTERBANK

    # Baixa Operacional Integral Intrabancária
    WRITE_OFF_INTRA_BANK

    # Baixa Operacional Parcial Intrabancária
    WRITE_OFF_INTRA_PARTIAL_INTRA_BANK
}

enum PaymentChannel {
    # Agências – Postos tradicionais
    AGENCY

    # Terminal de Auto-atendimento
    SELF_SERVICE

    # Internet (home / office banking)
    INTERNET

    # Correspondente bancário
    BANKING_CORRESPONDENT

    # Central de atendimento (Call Center)
    CALL_CENTER

    # Arquivo Eletrônico
    ELECTRONIC_FILE

    # DDA
    DDA
}

type WriteOffDetail {
    # TpPessoaPort (Tipo Pessoa Portador)
    carrierPersonType: PersonType

    # CPFCNPJPort (CPF ou CNPJ Portador)
    carrierDocument: String

    # Nom_RzSocPort (Nome do Portador)
    carrierName: String

    # DtHrRecbtTit (Data e Hora do Recebimento do Título)
    bankSlipReceiveAt: String

    # DtHrProcBaixaOperac (Data Hora Processamento Baixa Operacional)
    writeOffProcessedAt: String

    # VlrBaixaOperacTit (Valor Baixa Operacional Título, Utiliza separador decimal "ponto". Ex.: 1599.25)
    writeOffDetailBankSlipAmount: Float

    # NumCodBarrasBaixaOperac (Número Código Barras Baixa Operacional)
    writeOffBarCodeNumber: String

    # CanPgtoBaixaOperac (Canal Pagamento Baixa Operacional)
    writeOffPaymentChannel: PaymentChannel

    # MeioPgtoBaixaOperac (Meio Pagamento Baixa Operacional)
    writeOffPaymentMethod: PaymentMethod

    # IndrOpContg (Indicador Operação Contingência)
    contingencyOperationIndicator: Boolean
}


enum PaymentMethod {
    # Espécie
    IN_CASH

    # Débito em conta
    DEBIT

    # Cartão de crédito
    CREDIT

    # Cheque
    CHECK
}

type OperationalWriteOffDetail {
    # TpPessoaPort (Tipo Pessoa Portador)
    carrierPersonType: PersonType

    # CPFCNPJPort (CPF ou CNPJ Portador)
    carrierDocument: String

    # Nom_RzSocPort (Nome do Portador)
    carrierName: String

    # DtHrRecbtTit (Data e Hora do Recebimento do Título)
    bankSlipReceiveAt: String

    # DtHrProcBaixaOperac (Data Hora Processamento Baixa Operacional)
    operationalWriteOffProcessedAt: String

    # VlrBaixaOperacTit (Valor Baixa Operacional Título, Utiliza separador decimal "ponto". Ex.: 1599.25)
    operationalWriteOffDetailBankSlipAmount: Float

    # NumCodBarrasBaixaOperac (Número Código Barras Baixa Operacional)
    operationalWriteOffBarCodeNumber: String

    # CanPgtoBaixaOperac (Canal Pagamento Baixa Operacional)
    operationalWriteOffPaymentChannel: PaymentChannel

    # MeioPgtoBaixaOperac (Meio Pagamento Baixa Operacional)
    operationalWriteOffPaymentMethod: PaymentMethod

    # IndrOpContg (Indicador Operação Contingência)
    contingencyOperationIndicator: Boolean
}

type WriteOffCancellation {
    # DtHrCancelBaixa (Data Hora Cancelamento Baixa)
    canceledAt: String
}

type BankSlipWriteOff {
    # NumIdentcBaixaOperac (Número Identificação Baixa)
    identificationNumber: String

    # NumRefAtlBaixaOperac (Número Referência Atual Baixa)
    currentReferenceNumber: String

    # NumSeqAtlzBaixaOperac (Número Sequência Atualização Baixa)
    updateSequenceNumber: String

    # NumRefCadTitBaixaOperac (Número Referência Cadastro Título Baixa)
    registrationReferenceNumber: String

    # NumRefAtlCadTitBaixaOperac (Número Referência Atual Cadastro Título Baixa)
    currentRegistrationReferenceNumber: String

    # ISPBPartRecbdrBaixaOperac (ISPB Participante Recebedor Baixa)
    receivingParticipantISPB: String

    # CodPartRecbdrBaixaOperac (Código Participante Recebedor Baixa)
    receivingParticipantCode: String

    # TpBaixa (Tipo Baixa)
    type: BankSlipWriteOffType

    # SitReqBaixa (Situação da Requisição da Baixa)
    situation: WriteOffRequestSituation

    # DtHrSitBaixa (Data Hora Situação Baixa)
    updatedAt: String

    # (Detalhe de Baixa)
    details: [WriteOffDetail]

    # (Cancelamento de Baixa)
    cancellations: [WriteOffCancellation]
}


type OperationalWriteOffCancellation {
    # DtHrCancelBaixaOperac (Data Hora Cancelamento Baixa Operacional)
    canceledAt: String
}

type BankSlipOperationalWriteOff {
    # NumIdentcBaixaOperac (Número Identificação Baixa Operacional)
    identificationNumber: String

    # NumRefAtlBaixaOperac (Número Referência Atual Baixa Operacional)
    currentReferenceNumber: String

    # NumSeqAtlzBaixaOperac (Número Sequência Atualização Baixa Operacional)
    updateSequenceNumber: String

    # NumRefCadTitBaixaOperac (Número Referência Cadastro Título Baixa Operacional)
    registrationReferenceNumber: String

    # NumRefAtlCadTitBaixaOperac (Número Referência Atual Cadastro Título Baixa Operacional)
    currentRegistrationReferenceNumber: String

    # ISPBPartRecbdrBaixaOperac (ISPB Participante Recebedor Baixa Operacional)
    receivingParticipantISPB: String

    # CodPartRecbdrBaixaOperac (Código Participante Recebedor Baixa Operacional)
    receivingParticipantCode: String

    # TpBaixaOperac (Tipo Baixa Operacional)
    type: BankSlipWriteOffType

    # SitReqBaixaOperac (Situação da Requisição da Baixa Operacional)
    situation: OperationalWriteOffRequestSituation

    # DtHrSitBaixaOperac (Data Hora Situação Baixa Operacional)
    updatedAt: String

    # (Detalhe de Baixa Operacional)
    details: [OperationalWriteOffDetail]

    # (Cancelamento de Baixa Operacional)
    cancellations: [OperationalWriteOffCancellation]
}

enum EffectiveWriteOffType {
    # Baixa Efetiva Integral Interbancária
    EFFECTIVE_FULL_INTERBANK_WRITE_OFF

    # Baixa Efetiva Integral Intrabancária
    INTRA_BANK_FULL_EFFECTIVE_WRITE_OFF

    # Baixa Efetiva Integral por Solicitação do Cedente
    FULL_EFFECTIVE_WRITE_OFF_AT_ASSIGNOR

    # Baixa Efetiva Integral por envio para protesto
    FULL_EFFECTIVE_WRITE_OFF_BY_SUBMISSION_TO_PROTEST

    # Baixa Efetiva Integral por decurso de prazo
    FULL_EFFECTIVE_RETIREMENT_DUE_TO_EXPIRATION_OF_TERM

    # Baixa Efetiva Parcial Intrabancária
    EFFECTIVE_PARTIAL_INTRA_BANK_WRITE_OFF

    # Baixa Efetiva Parcial Interbancária
    EFFECTIVE_PARTIAL_INTERBANK_WRITE_OFF

    # Baixa Efetiva por solicitação da Instituição Destinatária
    EFFECTIVE_WRITE_OFF_AT_THE_REQUEST
}

type EffectiveWriteOff {
    # NumIdentcBaixaEft (Número Identificação Baixa Efetiva)
    identificationNumber: String

    # NumRefAtlBaixaEft (Número Referência Atual Baixa Efetiva)
    currentReferenceNumber: String

    # NumSeqAtlzBaixaEft (Número Sequência Atualização Baixa Efetiva)
    updateSequenceNumber: String

    # NumIdentcBaixaOperac (Número Identificação Baixa Operacional Baixa Efetiva)
    operationalWriteOffIdentificationNumber: String

    # TpBaixaEft (Tipo Baixa Efetiva)
    type: EffectiveWriteOffType

    # ISPBPartRecbdrBaixaEft (ISPB Participante Recebedor Baixa Efetiva)
    receivingParticipantISPB: String

    # CodPartRecbdrBaixaEft (Código Participante Recebedor Baixa Efetiva)
    receivingParticipantCode: String

    # DtHrProcBaixaEft Data Hora Processamento Baixa
    processingDateAndTime: String

    # VlrBaixaEftTit (Valor Baixa Efetiva Título, Utiliza separador decimal "ponto". Ex.: 1599.25)
    valueOfTheEffectiveWriteOffOfTheBankSlip: Float

    # NumCodBarrasBaixaEft (Número Código de Barras Baixa Efetiva)
    barCode: String

    # CanPgtoBaixaEft (Canal de Pagamento)
    paymentChannel: PaymentChannel

    # MeioPgtoBaixaEft (Meio de Pagamento)
    paymentMethod: PaymentMethod

    # DtHrSitBaixaEft (Data Hora Situação Baixa Efetiva)
    momentOfEffectiveWriteOffStatus: String
}

enum OperationalWriteOffRequestSituation {
    # Pendente
    PENDING

    # Enviado
    SENT

    # Rejeitado
    REJECTED

    # Efetivado
    EFFECTIVE
}

enum WriteOffRequestSituation {
    # Pendente
    PENDING

    # Enviado
    SENT

    # Rejeitado
    REJECTED

    # Efetivado
    EFFECTIVE
}

type BankSlipList {

    # (Total Itens)
    totalItems: Int

    # (Itens)
    items: [BankSlip]
}

input BankSlipRange {
    # ItemInicial (Item Inicial da lista requisitado)
    ini: Int

    # ItemFinal (Item Final da lista requisitado)
    fin: Int
}

input DateFilter {
    # DtVencTitIni (Data Vencimento Titulo Inicio) // DtRegTitIni (Data Registro Titulo Inicio)
    ini: Date

    # DtVencTitFim (Data Vencimento Titulo Fim) // DtRegTitFim (Data Registro Titulo Fim)
    fin: Date
}

enum BankSlipTypeFilter {
    # Próprio
    OWN

    # Terceiros
    THIRD_PARTY
}

input PersonFilter {
    # TpPessoaPagdr (Tipo Pessoa do Pagador Eletrônico)
    type: PersonType

    # CNPJ_CPFPagdr (CNPJ ou CPF do Pagador Eletrônico)
    document: String
}

input BankSlipFilter {
    # Filtro de pagador
    payer: PersonFilter

    # Filtro de tipo do boleto
    type: BankSlipTypeFilter

    # Filtro com intervalo ordinal de boletos
    range: BankSlipRange

    # Filtro com intervalo de datas de vencimento
    dueDate: DateFilter

    # Filtro com intervalo de datas de registro do boleto
    registration: DateFilter

    # Filtro de situação do boleto
    situation: BankSlipSituation

    # Filtro de situação de pagamento do boleto
    paymentSituation: PaymentSituation
}