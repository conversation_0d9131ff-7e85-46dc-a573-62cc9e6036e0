internal-auth:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7a
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

kafka:
  enabled: true
  bootstrap:
    servers:
      - "b-1.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-2.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-3.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-4.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-5.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-6.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-7.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-8.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-9.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-10.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-11.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"
      - "b-12.kafka.s0ns5h.c17.kafka.us-east-1.amazonaws.com:9092"

micronaut:
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 5
  metrics:
    tags:
      application: dda-service
      provider: friday
      env: staging
  http.services:
    dda-finder:
      url: http://baas-dda-finder.ms.qa
      read-timeout: 30s
      pool:
        max-connections: 5
        enabled: true

integrations:
  concessionaria:
    multicom:
      bucketName: "stg-multicom-files-pp"
      ftp:
        host: "edi01.ccsembratel.com.br"
        username: "PICPAY.MPAGOS.HML"
  dda:
    arbi:
      bucketName: ************-dda-files-lambda

aws:
  sqs:
    publisher:
      active-concessionaria-bills:
        queueName: concessionaria_bills_stg
      inactive-concessionaria-bills:
        queueName: concessionaria_inactive_bills_stg
      dda-activation:
        queueName: dda-activation
        delay: 900
      dda-bills-import:
        queueName: dda-bills-import
        delay: 5
      dda-bills-internal:
        queueName: dda-bills-internal
      dda-bill-notification-dlq:
        queueName: dda-bill-notification-dlq
      dda-register-response-dlq:
        queueName: dda-register-response-dlq
    handler:
      dda-bills-import:
        queueName: ${aws.sqs.publisher.dda-bills-import.queueName}
        enabled: true
        autoScaleWorkersInParallel: true
        consumers: 1
      kafka-router:
        queueName: kafka-router
      dda-opt-out-notification:
        queueName: dda-opt-out-notification
        enabled: true
        autoScaleWorkersInParallel: true
        consumers: 1
      dda-bill-notification-dlq:
        queueName: dda-bill-notification-dlq
        enabled: true
        autoScaleWorkersInParallel: false
        consumers: 1

fullImport:
  retryPolicy:
    maxHours: 1
    maxAttempts: 1

features:
  dda:
    files.enabled: false
    register.enabled: true
    notification:
      enabled: true
      reprocessor:
        enabled: true
      status:
        enabled: true
    deregister:
      enabled: true
      cron: "0/1 * * * *"

application:
  accountNumber: ************
  region: sa-east-1


logger:
  levels: INFO
  config: picpay-logback.xml

management.metrics.tags.env: staging