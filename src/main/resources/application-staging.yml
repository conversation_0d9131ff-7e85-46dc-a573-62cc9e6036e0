internal-auth:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7a
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

integrations:
  concessionaria:
    multicom:
      bucketName: "stg-multicom-files"
      ftp:
        host: "edi01.ccsembratel.com.br"
        username: "username" # FIXME
  dda:
    arbi:
      bucketName: ************-dda-files-lambda

redis:
  uri: redis://bill-payment-cache.aj9fwb.0001.use1.cache.amazonaws.com:6379/1

aws:
  sqs:
    publisher:
      active-concessionaria-bills:
        queueName: concessionaria_bills_stg
      inactive-concessionaria-bills:
        queueName: concessionaria_inactive_bills_stg
      dda-activation:
        queueName: dda-activation
        delay: 900
      dda-bills-import:
        queueName: dda-bills-import
        delay: 900
      dda-bills-internal:
        queueName: dda-bills-internal
      dda-bill-notification-dlq:
        queueName: dda-bill-notification-dlq
      dda-register-response-dlq:
        queueName: dda-register-response-dlq
    handler:
      dda-bills-import:
        queueName: ${aws.sqs.publisher.dda-bills-import.queueName}
        enabled: true
        autoScaleWorkersInParallel: true
        consumers: 1

fullImport:
  retryPolicy:
    maxHours: 15
    maxAttempts: 15

features:
  dda.files.enabled: true

application:
  accountNumber: ************
  region: us-east-1

management.metrics.tags.env: staging