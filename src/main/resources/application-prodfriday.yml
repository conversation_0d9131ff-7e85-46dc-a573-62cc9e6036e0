micronaut:
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 30

integrations:
  dda:
    arbi:
      bucketName: ************-dda-files-lambda

redis:
  uri: redis://bill-payment-cache.orwoqd.0001.use1.cache.amazonaws.com:6379/1

aws:
  sqs:
    publisher:
      active-concessionaria-bills:
        queueName: concessionaria_bills
      inactive-concessionaria-bills:
        queueName: concessionaria_inactive_bills
      dda-activation:
        queueName: dda-activation
        delay: 900
      dda-bills-import:
        queueName: dda-bills-import
        delay: 900
      dda-bills-internal:
        queueName: dda-bills-internal
      dda-bill-notification-dlq:
        queueName: dda-bill-notification-dlq
      dda-register-response-dlq:
        queueName: dda-register-response-dlq
    handler:
      dda-bills-import:
        queueName: ${aws.sqs.publisher.dda-bills-import.queueName}
        enabled: true
        autoScaleWorkersInParallel: true
        consumers: 10

fullImport:
  retryPolicy:
    maxHours: 15
    maxAttempts: 15

features:
  dda.lambda.enabled: true
  dda.files.enabled: true

tenants:
  MOTOROLA:
    bucket: ${integrations.dda.arbi.bucketName}
    protocol: HTTP
    configuration:
      username: b26185b9-8484-409d-9208-363b41dcd7f2
      password: FROM_AWS_SECRETS

application:
  accountNumber: ************
  region: us-east-1

management.metrics.tags.env: production