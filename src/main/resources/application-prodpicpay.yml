micronaut:
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 40
  metrics:
    tags:
      application: dda-service
      provider: friday
      env: production
  http.services:
    dda-finder:
      url: http://baas-dda-finder.ms.prod
      read-timeout: 30s
      pool:
        max-connections: 100
        enabled: true

kafka:
  enabled: true
  session.timeout.ms: 90000
  heartbeat.interval.ms: 15000
  max.poll:
    interval.ms: 300000
    records: 300

  bootstrap:
    servers:
      - "b-1.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-2.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-3.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-4.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-5.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-6.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-7.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-8.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-9.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-10.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-11.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"
      - "b-12.kafka-prod.zulbx9.c4.kafka.us-east-1.amazonaws.com:9092"

integrations:
  concessionaria:
    multicom:
      bucketName: "multicom-files-pp"
      ftp:
        host: "edi01.ccsembratel.com.br"
        username: "PICPAY.MPAGOS"
  dda:
    arbi:
      bucketName: ************-dda-files-lambda
aws:
  sqs:
    publisher:
      active-concessionaria-bills:
        queueName: concessionaria_bills
      inactive-concessionaria-bills:
        queueName: concessionaria_inactive_bills
      dda-activation:
        queueName: dda-activation
        delay: 900
      dda-bills-import:
        queueName: dda-bills-import
        delay: 900
      dda-bills-internal:
        queueName: dda-bills-internal
      dda-bill-notification-dlq:
        queueName: dda-bill-notification-dlq
      dda-register-response-dlq:
        queueName: dda-register-response-dlq
    handler:
      dda-bills-import:
        queueName: ${aws.sqs.publisher.dda-bills-import.queueName}
        enabled: true
        autoScaleWorkersInParallel: true
        consumers: 2
      kafka-router:
        queueName: kafka-router
        autoScaleWorkersInParallel: false
        consumers: 1
      dda-opt-out-notification:
        queueName: dda-opt-out-notification
        enabled: true
        autoScaleWorkersInParallel: true
        consumers: 2
      dda-bill-notification-dlq:
        queueName: dda-bill-notification-dlq
        enabled: true
        autoScaleWorkersInParallel: false
        consumers: 10
fullImport:
  retryPolicy:
    maxHours: 15
    maxAttempts: 15

features:
  dda:
    register.enabled: true
    notification:
      enabled: true
      reprocessor:
        enabled: true
      status:
        enabled: false
    files.enabled: false
    deregister:
      enabled: true
      cron: "40 2 * * *"

application:
  accountNumber: ************
  region: sa-east-1

logger:
  levels: INFO
  config: picpay-logback.xml

management.metrics.tags.env: production