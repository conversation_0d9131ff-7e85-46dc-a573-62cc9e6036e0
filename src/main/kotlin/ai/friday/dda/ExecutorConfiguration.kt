package ai.friday.dda

import io.micronaut.context.annotation.Factory
import io.opentelemetry.context.Context
import io.opentelemetry.extension.kotlin.asContextElement
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.concurrent.ExecutorService
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.asCoroutineDispatcher

@Factory
class ExecutorConfiguration {
    @Named("customConsumerExecutor")
    @Singleton
    fun customConsumerExecutor(): ExecutorService =
        ThreadPoolExecutor(
            1,
            500,
            10,
            TimeUnit.MINUTES,
            LinkedBlockingQueue(20),
        )

    @Named("jobExecutor")
    @Singleton
    fun jobExecutor(): ExecutorService =
        ThreadPoolExecutor(
            5,
            200,
            1,
            TimeUnit.MINUTES,
            LinkedBlockingQueue()
        )
}

fun ExecutorService.asCoroutineOtelTracingDispatcher() = this.asCoroutineDispatcher() + Context.current().asContextElement()