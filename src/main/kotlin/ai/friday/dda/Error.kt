package ai.friday.dda

import io.micronaut.http.HttpResponse

interface Error

open class Err(val message: String, val ex: Throwable? = null, val args: Map<String, Any> = mapOf()) : Error {
    override fun toString(): String {
        return if (ex == null) message else "${this::class.simpleName}#${ex::class.simpleName}"
    }
}

class ServerError(err: Throwable? = null) : Err("Internal Server Error", err)
class HttpClientError(res: HttpResponse<*>) : Err("Http Client Error", args = mapOf("status" to res.status))