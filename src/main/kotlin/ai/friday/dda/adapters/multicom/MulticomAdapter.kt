package ai.friday.dda.adapters.multicom

import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItem
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItemStatus
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoProvider
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoProviderService
import ai.friday.dda.andAppend
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.Document
import ai.friday.dda.app.bill.BarCode
import ai.friday.dda.app.bill.UNKNOWN_CONTRACT_CODE
import jakarta.inject.Singleton
import java.io.InputStream
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.math.roundToLong
import kotlin.streams.toList
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class MulticomAdapter : ConcessionariaDiretoProviderService {
    override fun processFile(inputStream: InputStream): List<ConcessionariaDiretoItem> {
        val itemDescription = "Plano Claro"
        inputStream.use {
            return inputStream.bufferedReader().lines()
                .dropWhile { it.contains("Identificacao,Fatura,Empresa,Valor,Vencimento,Id") }.map {
                    val markers = Markers.append("line", it.take(100))

                    val (cpf, digitableBarCode, _, amount, dueDate, providerBillId) = it.replace("\"", "")
                        .split(",")
                    val barCode = BarCode.ofDigitable(digitableBarCode)

                    if (barCode.contractCode() == UNKNOWN_CONTRACT_CODE) {
                        markers.andAppend("digitableBarCode", digitableBarCode)
                            .andAppend("companyCode", barCode.companyCode())

                        logger.error(markers, "MulticomAdapter#unknownCompanyCode")
                        null
                    } else {
                        ConcessionariaDiretoItem(
                            amount = convertToLong(amount),
                            barCode = barCode,
                            document = Document(cpf),
                            dueDate = LocalDate.parse(dueDate, DateTimeFormatter.ISO_DATE),
                            description = itemDescription,
                            provider = ConcessionariaDiretoProvider.MULTICOM,
                            providerBillId = providerBillId,
                            status = ConcessionariaDiretoItemStatus.ACTIVE,
                            inactivityCounter = 0,
                            lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime()
                        ).also { item ->
                            logger.info(markers.andAppend("item", item), "MulticomAdapter#processed")
                        }
                    }
                }.toList().filterNotNull()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MulticomAdapter::class.java)
    }
}

private operator fun <T> List<T>.component6() = this[5]

fun convertToLong(value: String): Long {
    if (value.isBlank()) {
        throw java.lang.IllegalArgumentException()
    }

    val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
    nf.applyPattern("#.##")
    return (nf.parse(value).toDouble() * 100.0).roundToLong()
}