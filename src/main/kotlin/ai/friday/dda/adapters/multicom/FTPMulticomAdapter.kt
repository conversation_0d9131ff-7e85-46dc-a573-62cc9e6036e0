package ai.friday.dda.adapters.multicom

import ai.friday.dda.PIC_PAY_ENV
import com.jcraft.jsch.ChannelSftp
import com.jcraft.jsch.JSch
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.io.InputStream
import java.util.Base64

@Requires(env = [PIC_PAY_ENV])
@ConfigurationProperties("integrations.concessionaria.multicom.ftp")
class MulticomFTPConfiguration @ConfigurationInject constructor(
    val host: String,
    val username: String,
    val key: String,
    val passphrase: String
)

@Requires(env = [PIC_PAY_ENV])
@Singleton
open class FTPMulticomAdapter(
    private val configuration: MulticomFTPConfiguration
) {

    fun listFiles(path: String): List<String> {
        val sftp = connect()
        return sftp.ls(path).mapNotNull {
            if (it is ChannelSftp.LsEntry) {
                it.filename.toString()
            } else {
                null
            }
        }
    }

    fun deleteFile(path: String) {
        val sftp = connect()
        return sftp.rm(path)
    }

    fun downloadFile(path: String): InputStream {
        val sftp = connect()
        return sftp.get(path)
    }

    private fun connect(): ChannelSftp {
        val ssh = JSch()
        val decodedKey = Base64.getDecoder().decode(configuration.key)
        ssh.addIdentity("ksftp", decodedKey, null, configuration.passphrase.toByteArray())
        val session = ssh.getSession(configuration.username, configuration.host, 22)
        session.setConfig("StrictHostKeyChecking", "no")
        session.connect()
        val channel = session.openChannel("sftp")
        channel.connect()
        return channel as (ChannelSftp)
    }
}