package ai.friday.dda.adapters.multicom

import jakarta.inject.Singleton
import java.io.InputStream
import java.security.Security
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.openpgp.PGPCompressedData
import org.bouncycastle.openpgp.PGPEncryptedDataList
import org.bouncycastle.openpgp.PGPException
import org.bouncycastle.openpgp.PGPLiteralData
import org.bouncycastle.openpgp.PGPOnePassSignatureList
import org.bouncycastle.openpgp.PGPPrivateKey
import org.bouncycastle.openpgp.PGPPublicKeyEncryptedData
import org.bouncycastle.openpgp.PGPSecretKeyRingCollection
import org.bouncycastle.openpgp.PGPUtil
import org.bouncycastle.openpgp.jcajce.JcaPGPObjectFactory
import org.bouncycastle.openpgp.operator.jcajce.JcaKeyFingerprintCalculator
import org.bouncycastle.openpgp.operator.jcajce.JcePBESecretKeyDecryptorBuilder
import org.bouncycastle.openpgp.operator.jcajce.JcePublicKeyDataDecryptorFactoryBuilder

@Singleton
class PGPStreamProcessor {

    init {
        Security.addProvider(BouncyCastleProvider())
    }

    fun decryptStream(
        encryptedStream: InputStream,
        keyStream: InputStream,
        password: String,
    ): InputStream {
        val encryptedDataList = getEncryptedDataList(encryptedStream)
        val (encryptedData, privateKey) = findKeys(encryptedDataList, keyStream, password)
        val literalData = getLiteralData(encryptedData, privateKey)

        return literalData.inputStream
    }

    private fun getEncryptedDataList(encryptedStream: InputStream): PGPEncryptedDataList {
        val decoderStream = PGPUtil.getDecoderStream(encryptedStream)
        val factory = JcaPGPObjectFactory(decoderStream)
        val nextObject = factory.nextObject()

        return if (nextObject is PGPEncryptedDataList) {
            nextObject
        } else {
            factory.nextObject() as PGPEncryptedDataList
        }
    }

    private fun findKeys(encryptedDataList: PGPEncryptedDataList, keyStream: InputStream, password: String): Pair<PGPPublicKeyEncryptedData, PGPPrivateKey> {
        val secretKeyRingCollection = PGPSecretKeyRingCollection(
            PGPUtil.getDecoderStream(keyStream), JcaKeyFingerprintCalculator()
        )

        encryptedDataList.encryptedDataObjects.forEach {
            val encryptedData = it as PGPPublicKeyEncryptedData
            val privateKey = findSecretKey(secretKeyRingCollection, encryptedData.keyID, password)

            if (privateKey != null) {
                return Pair(encryptedData, privateKey)
            }
        }

        throw IllegalStateException("secret key for message not found.")
    }

    private fun findSecretKey(secretKeyRingCollection: PGPSecretKeyRingCollection, keyID: Long, password: String): PGPPrivateKey? {
        val pgpSecKey = secretKeyRingCollection.getSecretKey(keyID) ?: return null
        return pgpSecKey.extractPrivateKey(JcePBESecretKeyDecryptorBuilder().setProvider("BC").build(password.toCharArray()))
    }

    private fun getLiteralData(encryptedData: PGPPublicKeyEncryptedData, privateKey: PGPPrivateKey): PGPLiteralData {
        val clear = encryptedData.getDataStream(JcePublicKeyDataDecryptorFactoryBuilder().setProvider("BC").build(privateKey))
        val plainFact = JcaPGPObjectFactory(clear)
        var message = plainFact.nextObject()
        if (message is PGPCompressedData) {
            val pgpFact = JcaPGPObjectFactory(message.dataStream)
            message = pgpFact.nextObject()
        }
        when (message) {
            is PGPLiteralData -> {
                return message
            }
            is PGPOnePassSignatureList -> {
                throw PGPException("encrypted message contains a signed message - not literal data.")
            }
            else -> {
                throw PGPException("message is not a simple encrypted file - type unknown.")
            }
        }
    }
}