package ai.friday.dda.adapters.picpay

import ai.friday.dda.PrintableSealedClass
import ai.friday.dda.andAppend
import ai.friday.dda.convert
import io.micronaut.context.annotation.Secondary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.multipart.MultipartBody
import jakarta.inject.Singleton
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

sealed class OptOutResult : PrintableSealedClass() {
    object Success : OptOutResult()
    object Failure : OptOutResult()
    object UnknownResponse : OptOutResult()
    object AccountAlreadyProcessed : OptOutResult()
    object AccountNotFound : OptOutResult()
    object UnprocessableEntity : OptOutResult()
    object BadRequest : OptOutResult()
}

data class OptOutResponse(val success: Boolean, val code: Int, val message: String)

@Secondary
@Singleton
class PicPayRegisterAdapter(
    @param:Client(value = "\${integrations.picpay.msBillsHost}") private val httpClient: HttpClient
) {

    companion object {
        private val LOG = LoggerFactory.getLogger(this::class.java)
    }

    fun sendOptOutNotification(consumerId: String): OptOutResult {
        val markers = Markers.append("consumer_id", consumerId)

        val requestId = UUID.randomUUID().toString()
        val body = MultipartBody.builder()
            .addPart("notification_id", "USER_DEACTIVATION_SUCCESS")
            .addPart("request_id", requestId)
            .build()

        markers.andAppend("request_id", requestId).andAppend("notification_id", "USER_DEACTIVATION_SUCCESS")

        val request = HttpRequest.POST("/v1/notifications/$consumerId", body).contentType(MediaType.MULTIPART_FORM_DATA)

        return try {
            httpClient.toBlocking().exchange(request, OptOutResponse::class.java).let { response ->
                markers.andAppend("statusResponse", response.status).andAppend("bodyResponse", response.body())
                LOG.info(markers, "PicPayRegisterAdapter#sendOptOutNotification")

                parseResult(response.status, response.body())
            }
        } catch (e: Exception) {
            when (e) {
                is HttpClientResponseException -> {
                    markers.andAppend("statusResponse", e.status).andAppend("bodyResponse", e.response.body())
                    parseResultError(e)
                }

                else -> OptOutResult.Failure
            }.also {
                LOG.error(markers, "PicPayRegisterAdapter#sendOptOutNotification", e)
            }
        }
    }

    private fun parseResult(httpStatus: HttpStatus, body: OptOutResponse?): OptOutResult = when (httpStatus) {
        HttpStatus.OK -> OptOutResult.Success
        HttpStatus.INTERNAL_SERVER_ERROR -> OptOutResult.Failure
        HttpStatus.UNPROCESSABLE_ENTITY ->
            when (body?.message) {
                "Usuário já possui DDA desativado" -> OptOutResult.AccountAlreadyProcessed
                "Usuário não encontrado" -> OptOutResult.AccountNotFound
                else -> OptOutResult.UnprocessableEntity
            }

        HttpStatus.BAD_REQUEST -> OptOutResult.BadRequest
        else -> OptOutResult.UnknownResponse
    }

    private fun parseResultError(e: HttpClientResponseException): OptOutResult =
        e.response.getBody(Argument.STRING).takeIf { it.isPresent }?.get()
            .takeIf { raw -> raw != null && raw.contains("message") && raw.contains("code") }
            ?.let { raw -> parseResult(e.response.status, convert<OptOutResponse>(raw)) }
            ?: OptOutResult.Failure
}