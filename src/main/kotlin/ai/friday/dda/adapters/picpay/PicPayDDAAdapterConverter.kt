package ai.friday.dda.adapters.picpay

import ai.friday.dda.adapters.picpay.BankSlipCalculationModel.Companion.toAmountCalculationModel
import ai.friday.dda.adapters.picpay.BankSlipSituation.Companion.toBillStatus
import ai.friday.dda.adapters.picpay.JDBankSlipSituation.Companion.toBillStatus
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.BillStatus
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.dda.app.DateUtils
import ai.friday.dda.app.DiscountInfo
import ai.friday.dda.app.FineData
import ai.friday.dda.app.InterestData
import ai.friday.dda.app.Recipient
import ai.friday.dda.app.RecipientChain
import ai.friday.dda.app.bill.BarCode
import ai.friday.dda.app.brazilTimeZone
import ai.friday.dda.app.getRecipientByPayer
import ai.friday.dda.app.interfaces.FinancialIdentifier
import ai.friday.dda.app.interfaces.FinancialInstitutionService
import ai.friday.dda.currencyToLong
import ai.friday.dda.getOrZero
import arrow.core.getOrElse
import jakarta.inject.Singleton
import java.math.BigDecimal
import java.time.LocalDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class PicPayDDAAdapterConverter(private val institutionService: FinancialInstitutionService) {
    private val logger = LoggerFactory.getLogger(PicPayDDAAdapterConverter::class.java)

    fun toDDARegister(data: BankSlip): BillRegisterData {
        val billType = BarCode.of(data.barcodeNumber).billType()

        val institution = data.recipientParticipantCode.toLongOrNull()?.let {
            institutionService.getInstitutionByCode(FinancialIdentifier.COMPE(it))
                .getOrElse {
                    logger.warn(
                        Markers.append("recipientParticipantCode", data.recipientParticipantCode),
                        "PicPayDDAAdapterConverter#institution_not_found"
                    )
                    null
                }
        }

        val assignor = institution?.name ?: data.recipientParticipantISPB

        val recipient = getRecipientChainOrDefault(data, Recipient(assignor))

        return with(data) {
            val currentCalculation = getCurrentCalculation()

            val extras = currentCalculation?.run { fineAmount.getOrZero() + interestAmount.getOrZero() - discountAmount.getOrZero() - rebateAmount.getOrZero() }.getOrZero()
            // currentCalculation?.totalAmount apresenta valores sem clara explicacao, eventualmente multiplicado por cem
            val amountTotal = amount + extras

            val paymentData = calculatePaymentData(data)

            BillRegisterData(
                barCode = BarCode.of(barcodeNumber),
                assignor = assignor,
                billType = billType,
                recipient = recipient,
                payerName = payer.name,
                payerDocument = formatDocument(payer.document, PersonType.valueOf(payer.type)),
                amount = currencyToLong(amount),
                interest = currentCalculation?.interestAmount.currencyToLong(),
                fine = currentCalculation?.fineAmount.currencyToLong(),
                discount = currentCalculation?.discountAmount.currencyToLong(),
                amountTotal = amountTotal.let(::currencyToLong),
                amountPaid = paymentData.amountPaid,
                expirationDate = paymentDeadline, // FIXME: ver se é obrigatório ter
                dueDate = expirationDate, // calculations?.dueDate, // FIXME: ver se é obrigatório ter
                paidDate = paymentData.paymentDate?.toLocalDate(),
                fichaCompensacaoType = BankSlipSpeciesCode.toFichaCompensacaoType(speciesCode),
                abatement = rebateAmount?.toDouble(),
                rebate = rebateAmount?.let(::currencyToLong),
                idNumber = identificationNumber,
                amountCalculationModel = toAmountCalculationModel(calculationModel),
                interestData = interest?.let {
                    InterestData(
                        type = BankSlipInterestCode.toInterestType(it.code),
                        value = it.percentage,
                        date = it.moment
                    )
                },
                fineData = fine?.let {
                    FineData(
                        type = BankSlipFineType.toFineType(it.amountType),
                        value = it.amount,
                        date = it.moment
                    )
                },
                recipientChain = RecipientChain(
                    sacadorAvalista = guarantorDrawer?.toRecipient(),
                    originalBeneficiary = originalBeneficiary?.toRecipient(),
                    finalBeneficiary = finalBeneficiary?.toRecipient()
                ),
                discountData = discounts?.let {
                    Discount.toDiscountData(it)
                },
                discounts = discounts?.map {
                    DiscountInfo(
                        value = it.amount.currencyToLong(),
                        type = BankSlipDiscountType.toDiscountType(it.amountType),
                        date = it.moment
                    )
                },
                paymentLimitTime = DEFAULT_PAYMENT_LIMIT_TIME,
                settleDate = getLocalDate(), // FIXME: entender se precisamos fazer algo a mais com esse campo
                paymentStatus = PaymentSituation.toPaymentStatusType(paymentSituation),
                billStatus = calculateBillStatus(situation, bankSlipSituation)
            )
        }
    }

    private companion object {
        const val DEFAULT_PAYMENT_LIMIT_TIME = "20:00" // FIXME: check this time

        fun currencyToLong(value: BigDecimal): Long = value.multiply(BigDecimal.valueOf(100)).toLong()

        private fun calculatePaymentData(bankSlip: BankSlip): BankSlipPaymentData {
            val dateCutOff = when (bankSlip.speciesCode) {
                BankSlipSpeciesCode.CREDIT_CARD ->
                    bankSlip.expirationDate.minusDays(12).atStartOfDay()

                else -> LocalDateTime.MIN // no date constraints'll be applied
            }

            val writeOffs = bankSlip.writeOffs
                ?.asSequence()
                ?.filter { it.situation != OperationalWriteOffRequestSituation.REJECTED }
                ?.mapNotNull { it.details }
                ?.flatten()
                ?.filter { it.isValid(dateCutOff) }

            val sumWriteOffs = writeOffs
                ?.mapNotNull { it.writeOffDetailBankSlipAmount }
                ?.sumOf { it } ?: BigDecimal.ZERO

            val lastPaymentDate = writeOffs?.mapNotNull { it.processedAt() }?.maxOrNull()

            return BankSlipPaymentData(currencyToLong(sumWriteOffs), lastPaymentDate)
        }
    }

    private fun calculateBillStatus(situation: BankSlipSituation?, jdSituation: JDBankSlipSituation?): BillStatus? =
        // new property has priority only if it is not null
        jdSituation?.toBillStatus() ?: situation.toBillStatus()
}

fun BankSlip.rtt(): Long? = runCatching {
    val slipDates = listOf(updatedAt, dispatchDate?.atStartOfDay())
    val writeOffsUpdates = writeOffs?.flatMap { listOf(it.updatedAt) } ?: emptyList()

    val today = getLocalDate()

    (slipDates + writeOffsUpdates).filterNotNull()
        .filter { it >= today.atStartOfDay() }
        .maxByOrNull { it }?.let { DateUtils.secondsUntilNow(it.atZone(brazilTimeZone)) }
}.getOrNull()

private fun WriteOffDetail.isValid(dateLimit: LocalDateTime): Boolean {
    val operationWriteOffDate = writeOffProcessedAt ?: bankSlipReceiveAt
    return operationWriteOffDate != null && dateLimit.isBefore(operationWriteOffDate)
}

private fun WriteOffDetail.processedAt() = writeOffProcessedAt ?: bankSlipReceiveAt

private fun firstNotNullAndNotEmpty(vararg elements: String?): String? {
    return elements.firstOrNull {
        !it.isNullOrEmpty()
    }
}

private fun getRecipientChainOrDefault(response: BankSlip, default: Recipient): Recipient =
    getRecipientChain(response).getRecipientByPayer(response.payer.document, default.name)

private fun getRecipientChain(response: BankSlip) = RecipientChain(
    sacadorAvalista = response.guarantorDrawer?.takeIf { it.document?.isNotEmpty() ?: false && it.name?.isNotEmpty() ?: false }
        ?.let {
            Recipient(
                name = it.name ?: "",
                document = formatDocumentSacadorAvalista(
                    response.guarantorDrawer.document,
                    response.guarantorDrawer.type
                )
            )
        },
    originalBeneficiary = firstNotNullAndNotEmpty(
        response.originalBeneficiary?.name,
        response.originalBeneficiary?.tradeName
    )?.let {
        Recipient(
            name = it,
            document = formatDocument(response.originalBeneficiary?.document, response.originalBeneficiary?.type)
        )
    },
    finalBeneficiary = firstNotNullAndNotEmpty(
        response.finalBeneficiary?.name,
        response.finalBeneficiary?.tradeName
    )?.let {
        Recipient(
            name = it,
            document = formatDocument(response.finalBeneficiary?.document, response.finalBeneficiary?.type)
        )
    }
)

private fun formatDocumentSacadorAvalista(document: String?, type: PersonType?): String? {
    if (document.isNullOrEmpty()) {
        return document
    }
    return when (type) {
        PersonType.NATURAL -> document.takeLast(11).padStart(11, '0')
        PersonType.LEGAL -> document.takeLast(14).padStart(14, '0')
        else -> ""
    }
}

private fun formatDocument(document: String?, type: PersonType?): String? {
    if (document.isNullOrEmpty()) {
        return document
    }
    return when (type) {
        PersonType.NATURAL -> document.padStart(11, '0')
        PersonType.LEGAL -> document.padStart(14, '0')
        else -> ""
    }
}

internal fun BankSlip.getCurrentCalculation(): Calculation? {
    val current = calculations
        ?.asSequence()
        ?.filter { it.dueDate != null }
        ?.filter { !it.dueDate!!.isBefore(DateUtils.today()) }
        ?.minByOrNull { it.dueDate!! }

    return current ?: calculations?.firstOrNull { it.dueDate == null }
}

data class BankSlipPaymentData(val amountPaid: Long?, val paymentDate: LocalDateTime?)