package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.aws.SQSMessagePublisher
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.Topics.DDA_INBOUND_TOPIC_NAME
import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.dda.DDARegisterService
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.app.interfaces.ddaRegisterResponseDlqBean
import ai.friday.dda.convert
import io.micronaut.configuration.kafka.annotation.ErrorStrategy
import io.micronaut.configuration.kafka.annotation.ErrorStrategyValue
import io.micronaut.configuration.kafka.annotation.KafkaListener
import io.micronaut.configuration.kafka.annotation.OffsetReset
import io.micronaut.configuration.kafka.annotation.Topic
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.slf4j.LoggerFactory

@Singleton
@KafkaListener(
    groupId = "\${kafka.dda-register-response.group_id:friday-prod-dda-register-response-consumer}",
    threads = 1,
    offsetReset = OffsetReset.EARLIEST,
    errorStrategy = ErrorStrategy(value = ErrorStrategyValue.RETRY_ON_ERROR, retryCount = 3)
)
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "features.dda.register.enabled", value = "true")
)
open class PicPayDDARegisterResponseHandler(
    private val service: DDARegisterService,
    private val sqsMessagePublisher: SQSMessagePublisher,
    @Named(ddaRegisterResponseDlqBean) private val dlqConfiguration: MessagePublisherConfiguration
) : CustomKafkaListenerExceptionHandler(DLQConfiguration(sqsMessagePublisher, dlqConfiguration.queueName)) {
    private val loggerName = "PicPayDDARegisterResponseHandler"
    private val logger = LoggerFactory.getLogger(loggerName)

    @NewSpan
    @Topic(DDA_INBOUND_TOPIC_NAME)
    open fun onMessage(record: ConsumerRecord<String, String>) {
        val markers = with(record) {
            Markers.append("key", key()).and("partition" to partition(), "offset" to offset())
        }

        val responseEvent = kotlin.runCatching { convert<PicPayResponseEventTO>(record.value()) }.getOrElse {
            logger.error(markers, loggerName, it)
            throw it
        }

        markers.andAppend("event", responseEvent)

        try {
            val body = responseEvent.body

            val (event, handler) = when (body) {
                is PicPayDDAEventSourceOptInApproved -> "opt-in-approved" to service::handleOptInApproved
                is PicPayDDAEventSourceOptInReproved -> "opt-in-reproved" to service::handleOptInReproved
                is PicPayDDAEventSourceOptInError ->
                    // https://via1pagamentos.slack.com/archives/C03PA35GSDV/p1675192385906219
                    // an opt-out request can result in an opt-in error
                    "opt-in-error" to
                        when {
                            body.isDDAAlreadyExists() -> service::handleOptInApproved
                            body.isDDANotEligibleForOptOut() -> service::handleOptOutApproved
                            body.isOriginatedByOptOut() -> null
                            else -> service::handleOptInReproved
                        }

                is PicPayDDAEventSourceOptOutApproved -> "opt-out-approved" to service::handleOptOutApproved
                is PicPayDDAEventSourceOptOutError -> "opt-out-error" to if (body.isDDANotRegistered()) service::handleOptOutApproved else null
                else -> "unknown" to null
            }

            markers.and("eventName" to event, "document" to body.doc)

            if (handler == null) return logger.warn(markers, loggerName)

            handler(body.doc).fold(
                { logger.error(markers.and("errorMessage" to it), loggerName) },
                { logger.info(markers, loggerName) }
            )
        } catch (e: Exception) {
            logger.error(markers, loggerName, e)
            throw e
        }
    }
}

@Singleton
@KafkaListener(
    groupId = "friday-log-dda-register-response-consumer",
    threads = 1,
    offsetReset = OffsetReset.EARLIEST,
    errorStrategy = ErrorStrategy(value = ErrorStrategyValue.RETRY_ON_ERROR, retryCount = 3, retryDelay = "60s")
)
@Requires(env = [PIC_PAY_ENV])
open class PicPayDDARegisterResponseLogHandler {
    private val logger = LoggerFactory.getLogger("PicPayDDARegisterResponseLogHandler")

    @NewSpan
    @Topic(DDA_INBOUND_TOPIC_NAME)
    open fun onMessage(record: ConsumerRecord<String, String>) {
        logger.info(
            Markers.append("key", record.key())
                .andAppend("value", record.value())
                .andAppend("offset", record.offset())
                .andAppend("partition", record.partition()),
            "PicPayDDARegisterResponseLogHandler"
        )
    }
}

fun PicPayDDAEventSourceOptInError.isDDAAlreadyExists() = source.errors.any { it.code == "DDA_ALREADY_EXISTS" }
fun PicPayDDAEventSourceOptOutError.isDDANotRegistered() = source.errors.any { it.code == "PAYER_NOT_REGISTERED" }

fun PicPayDDAEventSourceOptInError.isOriginatedByOptOut() = source.payload.contains("DDA_OPT_OUT_FROM_PICPAY_WAS_MADE")
fun PicPayDDAEventSourceOptInError.isDDANotEligibleForOptOut() =
    isOriginatedByOptOut() && source.errors.any {
        it.code == "PAYER_NOT_REGISTERED" ||
            (it.code == "INTEGRATION_ERROR" && it.message == "Situação do Pagador Eletrônico não permite Exclusão")
    }