package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.aws.SQSMessagePublisher
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.Topics.DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME
import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.QueueMessage
import ai.friday.dda.app.dda.DDAEventService
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.app.interfaces.ddaBillNotificationDlqBean
import ai.friday.dda.app.metrics.AbstractFridayCountMetrics
import ai.friday.dda.app.metrics.metricRegister
import ai.friday.dda.asCoroutineOtelTracingDispatcher
import ai.friday.dda.convert
import ai.friday.dda.measure
import ai.friday.dda.parallelMap
import arrow.core.getOrHandle
import io.micronaut.configuration.kafka.annotation.ErrorStrategy
import io.micronaut.configuration.kafka.annotation.ErrorStrategyValue
import io.micronaut.configuration.kafka.annotation.KafkaListener
import io.micronaut.configuration.kafka.annotation.OffsetReset
import io.micronaut.configuration.kafka.annotation.OffsetStrategy
import io.micronaut.configuration.kafka.annotation.Topic
import io.micronaut.configuration.kafka.exceptions.KafkaListenerException
import io.micronaut.configuration.kafka.exceptions.KafkaListenerExceptionHandler
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.Collections
import java.util.UUID
import java.util.concurrent.ExecutorService
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers.append
import org.apache.kafka.clients.consumer.Consumer
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.OffsetAndMetadata
import org.apache.kafka.common.TopicPartition
import org.slf4j.LoggerFactory

@Singleton
@KafkaListener(
    groupId = "friday-prod-dda-consumer-002",
    threads = 5,
    offsetReset = OffsetReset.EARLIEST,
    offsetStrategy = OffsetStrategy.DISABLED,
    errorStrategy = ErrorStrategy(value = ErrorStrategyValue.RETRY_ON_ERROR, retryCount = 3),
    autoStartup = true,
    batch = true
)
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "features.dda.notification.enabled", value = "true")
)
open class PicPayBillNotificationKafkaHandler(
    private val service: DDAEventService,
    private val sqsMessagePublisher: SQSMessagePublisher,
    @Named(ddaBillNotificationDlqBean) private val ddaDlqConfiguration: MessagePublisherConfiguration,
    @Named("consumer") private val executor: ExecutorService,
    private val statusEmitter: PicPayBillNotificationStatusEmitter? = null,
) : KafkaListenerExceptionHandler {
    private val loggerName = "PicPayBillNotificationHandler"
    private val logger = LoggerFactory.getLogger(loggerName)

    @NewSpan
    @Topic(DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME)
    open fun onMessage(
        values: List<String>,
        offsets: List<Long>,
        partitions: List<Int>,
        kafkaConsumer: Consumer<String, String>
    ) {
        val executionId = UUID.randomUUID().toString()

        values.mapIndexed { i, v ->
            KafkaRecord(
                value = v,
                offset = offsets[i],
                partition = partitions[i],
                cid = executionId
            )
        }.groupBy { it.partition }
            .forEach { (partition, records) ->
                records.also {
                    val markers = append("lastOffset", records.last().offset).and(
                        "firstOffset" to records.first().offset,
                        "partition" to partition,
                        "partitionSize" to records.size,
                        "executionId" to executionId
                    )
                    logger.info(markers, "$loggerName#partitionStart")
                }
                    .sortedBy { it.offset }
                    .chunked(30)
                    .forEach { chunk ->
                        val markers = append("lastOffset", chunk.last().offset).and(
                            "firstOffset" to chunk.first().offset,
                            "partition" to partition,
                            "chunkSize" to chunk.size,
                            "executionId" to executionId
                        )

                        try {
                            logger.info(markers, "$loggerName#chunkStart")

                            runBlocking(executor.asCoroutineOtelTracingDispatcher()) {
                                chunk.parallelMap { record -> doProcess(record) }
                            }

                            doCommit(chunk, partition, kafkaConsumer, executionId)
                        } catch (e: Throwable) {
                            logger.error(markers, "$loggerName#chunkError", e)
                            throw e
                        }
                    }
            }
    }

    private fun sendToDLQ(record: KafkaRecord) = sqsMessagePublisher.sendMessage(
        QueueMessage(queueName = ddaDlqConfiguration.queueName, jsonObject = record.value)
    )

    private fun doCommit(
        chunk: List<KafkaRecord>,
        partition: Int,
        kafkaConsumer: Consumer<String, String>,
        executionId: String
    ) {
        val markers = append("lastOffset", chunk.last().offset)
            .andAppend("firstOffset", chunk.first().offset)
            .andAppend("committedOffset", chunk.last().offset + 1)
            .andAppend("partition", partition)
            .andAppend("chunkSize", chunk.size)
            .andAppend("executionId", executionId)

        try {
            kafkaConsumer.commitSync(
                Collections.singletonMap(
                    TopicPartition(DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME, partition),
                    OffsetAndMetadata(chunk.last().offset + 1)
                )
            )
            logger.info(markers, "$loggerName#commit")
        } catch (e: Throwable) {
            logger.error(markers.andAppend("error", e.message), "$loggerName#commitError", e)
            throw e
        }
    }

    override fun handle(exception: KafkaListenerException?) {
        val record = exception?.consumerRecord?.get()

        val markers = append("key", record?.key())
            .andAppend("value", record?.value())
            .andAppend("offset", record?.offset())
            .andAppend("partition", record?.partition())
            .andAppend("cause", exception?.message)

        logger.error(markers, "$loggerName#kafkaException")
    }

    @NewSpan
    open fun doProcess(record: KafkaRecord) {
        val markers = append("value", record.value)
            .andAppend("offset", record.offset)
            .andAppend("partition", record.partition)
            .andAppend("cid", record.cid)

        runCatching {
            val event = convert<PicPayDDAEvent>(record.value).also { statusEmitter?.notifyDDAReceived(it) }

            markers.and("document" to event.payer.document, "cip_id" to event.bankSlip.id)

            val (result, elapsed) = measure { service.handleEvent(cipID = event.bankSlip.id, requestId = event.requestId(), type = event.type) }
            markers.and("elapsedTime" to elapsed)

            // unwrap either approach
            result.getOrHandle {
                markers.and("reason" to it.toString())
                throw it.ex ?: Throwable("Unknown error")
            }
        }.onSuccess {
            metricRegister(PicPayDDABillNotificationMetric(), "result" to "SUCCESS")

            logger.info(markers, "$loggerName#doProcess")
        }.onFailure {
            metricRegister(PicPayDDABillNotificationMetric(), "result" to "FAILED")
            logger.error(markers, "$loggerName#doProcess", it)

            sendToDLQ(record)
        }
    }
}

@Singleton
@KafkaListener(
    groupId = "friday-log-dda-consumer",
    threads = 1,
    offsetReset = OffsetReset.EARLIEST,
    errorStrategy = ErrorStrategy(value = ErrorStrategyValue.RETRY_ON_ERROR, retryCount = 3, retryDelay = "60s")
)
@Requires(env = [PIC_PAY_ENV])
open class PicPayKafkaBillNotificationLogHandler {
    private val logger = LoggerFactory.getLogger("PicPayKafkaBillNotificationLogHandler")

    @NewSpan
    @Topic(DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME)
    open fun onMessage(record: ConsumerRecord<String, String>) {
        logger.info(
            append("key", record.key())
                .andAppend("value", record.value())
                .andAppend("offset", record.offset())
                .andAppend("partition", record.partition()),
            "PicPayKafkaBillNotificationLogHandler"
        )
    }
}

data class KafkaRecord(val value: String, val offset: Long, val partition: Int, val cid: String? = null)

class PicPayDDABillNotificationMetric : AbstractFridayCountMetrics()