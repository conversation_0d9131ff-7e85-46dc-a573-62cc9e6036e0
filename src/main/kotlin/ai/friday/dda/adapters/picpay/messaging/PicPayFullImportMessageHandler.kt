package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.NotFoundErr
import ai.friday.dda.app.dda.DDAFullImportService
import ai.friday.dda.app.dda.FullImportCommand
import ai.friday.dda.app.dda.FullImportResult
import ai.friday.dda.app.interfaces.MessageHandlerConfiguration
import ai.friday.dda.app.interfaces.ddaBillsImportBean
import ai.friday.dda.convert
import ai.friday.dda.measure
import arrow.core.getOrHandle
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

@Singleton
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "aws.sqs.handler.dda-bills-import.enabled", notEquals = "false")
)
open class PicPayFullImportMessageHandler(
    @Named(ddaBillsImportBean) private val billsImportConfig: MessageHandlerConfiguration,
    cli: SqsClient,
    properties: SQSProperties,
    private val service: DDAFullImportService
) : AbstractParallelSQSHandler(cli, properties, billsImportConfig, maxParallelism = 10) {
    private val logger = LoggerFactory.getLogger(PicPayFullImportMessageHandler::class.java)

    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val markers = Markers.append("event.body", m.body()).and("event.attributes" to m.attributes())

        val event = convert<DDAFullImportTO>(m.body())

        markers.andAppend("document", event.document)

        val command = FullImportCommand(
            document = event.document,
            createdAtInMillis = m.extractCreatedAtInMillis(),
            attempts = m.extractAttempts(),
            activateDDA = event.activateDDA,
        )

        val (response, elapsed) = measure { service.handleFullImport(command) }
        markers.and("elapsedTime" to elapsed, "result" to (response.orNull() ?: "error"))

        val result = response.getOrHandle {
            val deleteMessage = when (it) {
                is NotFoundErr -> true
                else -> false
            }
            logger.error(markers.and("error" to it), "PicPayFullImportMessageHandler")
            return SQSHandlerResponse(deleteMessage)
        }

        logger.info(markers, "PicPayFullImportMessageHandler")

        return when (result) {
            is FullImportResult.Success, is FullImportResult.RetryPolicyExceeded -> SQSHandlerResponse(true)
            is FullImportResult.NoContent -> SQSHandlerResponse(false)
        }
    }

    private fun Message.extractCreatedAtInMillis() =
        attributes().getOrElse(MessageSystemAttributeName.APPROXIMATE_FIRST_RECEIVE_TIMESTAMP) {
            throw IllegalStateException("message should contain APPROXIMATE_FIRST_RECEIVE_TIMESTAMP attribute")
        }.toLong()

    private fun Message.extractAttempts() =
        attributes().getOrElse(MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT) {
            throw IllegalStateException("message should contain APPROXIMATE_RECEIVE_COUNT attribute")
        }.toLong()

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        val markers = Markers.append("event.body", m.body())
        logger.error(markers, "PicPayFullImportMessageHandler", e)
        return SQSHandlerResponse(false)
    }
}