package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.KAFKA_APPLICATION_NAME
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.Topics.DDA_BANKSLIP_NOTIFICATIONS_STATUS_TOPIC_NAME
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.brazilTimeZone
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "features.dda.notification.status.enabled", value = "true")
)
class PicPayBillNotificationStatusEmitter(private val kafkaPublisher: PicPayKafkaPublisher) {
    val logger: Logger = LoggerFactory.getLogger(PicPayBillNotificationStatusEmitter::class.java)

    fun notifyDDAReceived(event: PicPayDDAEvent) {
        val eventStatusNotification = PicPayDDAReceivedEventTO.create(event.bankSlip.id)
        val markers = append("status_notification", eventStatusNotification)

        runCatching {
            kafkaPublisher.send(
                topic = DDA_BANKSLIP_NOTIFICATIONS_STATUS_TOPIC_NAME,
                correlationId = event.requestId(),
                applicationName = KAFKA_APPLICATION_NAME,
                event = eventStatusNotification
            )
        }.onSuccess {
            logger.info(markers, "PicPayBillNotificationStatusEmitter#statusNotificationSent")
        }.onFailure {
            logger.error(markers, "PicPayBillNotificationStatusEmitter#statusNotificationError", it)
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PicPayDDAReceivedEventTO(val body: PicPayDDAReceivedEventBody) {
    companion object {
        private const val FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS"
        private val dateTimeFormatter = DateTimeFormatter.ofPattern(FORMAT).withZone(brazilTimeZone)

        fun create(bankslipId: String, sentAt: ZonedDateTime = BrazilZonedDateTimeSupplier.getZonedDateTime()) =
            PicPayDDAReceivedEventTO(
                PicPayDDAReceivedEventBody(
                    event = KafkaConstants.DDA_RECEIVED_EVENT_NAME,
                    source = PicPayDDAReceivedSource(bankslipId = bankslipId, sentAt = sentAt.format(dateTimeFormatter))
                )
            )
    }
}

data class PicPayDDAReceivedEventBody(val event: String, val source: PicPayDDAReceivedSource)

data class PicPayDDAReceivedSource(val bankslipId: String, val sentAt: String)