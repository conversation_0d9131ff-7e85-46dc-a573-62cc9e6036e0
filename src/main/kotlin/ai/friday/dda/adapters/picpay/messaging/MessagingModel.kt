package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.BillStatus
import ai.friday.dda.app.PaymentStatus
import ai.friday.dda.app.dda.DDAProvider

data class DDABillMessageTO(
    val ddaProvider: DDAProvider,
    val status: BillRegisterStatus,
    val bill: BillRegisterData,
    val requestId: String,
    val operationType: String
) {
    companion object {
        private val notPayableStatus = listOf(PaymentStatus.BAIXA_REALIZADA, PaymentStatus.BLOQUEADO)

        fun from(
            bill: BillRegisterData,
            requestId: String,
            ddaProvider: DDAProvider = DDAProvider.PICPAY,
            operationType: String
        ): DDABillMessageTO {
            val isOpen = (bill.billStatus == BillStatus.ABERTO)
            val isNotPayable = !isOpen || notPayableStatus.contains(bill.paymentStatus)

            val isAlreadyPaid = (bill.paymentStatus == PaymentStatus.PAGAMENTO_REGISTRADO_EM_BAIXA_OPERACIONAL)

            return DDABillMessageTO(
                ddaProvider = ddaProvider,
                bill = bill,
                status = BillRegisterStatus(
                    notPayable = isNotPayable,
                    alreadyPaid = isAlreadyPaid || (bill.amountPaid ?: 0) > 0
                ),
                requestId = requestId,
                operationType = operationType
            )
        }
    }
}

data class BillRegisterStatus(val notPayable: Boolean, val alreadyPaid: Boolean)

data class DDAFullImportTO(val document: String, val activateDDA: Boolean = true)