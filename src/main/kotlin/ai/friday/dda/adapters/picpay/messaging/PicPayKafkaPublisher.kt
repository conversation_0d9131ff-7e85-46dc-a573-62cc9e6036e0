package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.KAFKA_APPLICATION_NAME
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.Topics.DDA_OUTBOUND_TOPIC_NAME
import io.micronaut.configuration.kafka.annotation.KafkaClient
import io.micronaut.configuration.kafka.annotation.Topic
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.messaging.annotation.MessageHeader
import org.apache.kafka.clients.producer.ProducerConfig

@KafkaClient(
    batch = true,
    properties = [
        Property(name = ProducerConfig.ACKS_CONFIG, value = "1"),
        Property(name = ProducerConfig.LINGER_MS_CONFIG, value = "100"),
        Property(name = ProducerConfig.RETRIES_CONFIG, value = "3"),
        Property(name = ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, value = "20"),
        Property(name = ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, value = "30100")
    ]
)
@Requires(env = [PIC_PAY_ENV])
interface PicPayKafkaPublisher {
    @Topic(DDA_OUTBOUND_TOPIC_NAME)
    fun send(
        @MessageHeader("correlation-id") correlationId: String,
        @MessageHeader("application-name") applicationName: String = KAFKA_APPLICATION_NAME,
        event: PicPayDDAOptInEventTO
    )

    @Topic(DDA_OUTBOUND_TOPIC_NAME)
    fun send(
        @MessageHeader("correlation-id") correlationId: String,
        @MessageHeader("application-name") applicationName: String = KAFKA_APPLICATION_NAME,
        event: PicPayDDAOptOutEventTO
    )

    fun send(
        @Topic topic: String,
        @MessageHeader("correlation-id") correlationId: String = eventUUID(),
        @MessageHeader("application-name") applicationName: String = KAFKA_APPLICATION_NAME,
        event: Any
    )
}