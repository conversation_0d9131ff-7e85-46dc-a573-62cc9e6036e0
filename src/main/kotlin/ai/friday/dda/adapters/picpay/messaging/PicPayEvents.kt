package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.adapters.picpay.PersonType
import ai.friday.dda.convert
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName

private const val PICPAY_ISPB = "********"

@JsonIgnoreProperties(ignoreUnknown = true)
data class PicPayDDAOptInEventTO(
    val body: PicPayDDAOptInEventBody
)

data class PicPayDDAOptInEventBody(
    val event: String,
    val source: DDAOptInRequestSource
)

data class DDAOptInRequestSource(
    val personType: String = "NATURAL",
    val document: String,
    val ispb: String = PICPAY_ISPB,
    val accounts: List<DDAOptInRequestAccount>
)

data class DDAOptInRequestAccount(
    val agencyType: String = "PHYSICAL",
    val agencyNumber: String = "0001",
    val accountType: String = "CHECKING_ACCOUNT",
    val accountNumber: String = "*********"
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PicPayDDAOptOutEventTO(
    val body: PicPayDDAOptOutEventBody
)

data class PicPayDDAOptOutEventBody(
    val source: DDAOptOutRequestSource
) {
    val event: String = "DDA_OPT_OUT_FROM_PICPAY_WAS_MADE"
}

data class DDAOptOutRequestSource(
    val personType: String = "NATURAL",
    val ispb: String = PICPAY_ISPB,
    val document: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PicPayResponseEventTO(
    val body: PicPayDDAEventSource
)

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "event",
    defaultImpl = DefaultPicPayDDAEventSource::class
)
sealed class PicPayDDAEventSource {
    abstract val doc: String
}

@JsonTypeName("DDA_OPT_IN_FROM_CIP_WAS_APPROVED")
data class PicPayDDAEventSourceOptInApproved(val source: PicPayDDARegisterResponseSource) : PicPayDDAEventSource() {
    override val doc = source.document
}

@JsonTypeName("DDA_OPT_IN_FROM_CIP_WAS_REPROVED")
data class PicPayDDAEventSourceOptInReproved(val source: PicPayDDARegisterResponseSource) : PicPayDDAEventSource() {
    override val doc = source.document
}

@JsonTypeName("DDA_OPT_IN_WAS_PROCESSED_WITH_ERROR")
data class PicPayDDAEventSourceOptInError(val source: PicPayDDARegisterResponseErrorSource) : PicPayDDAEventSource() {
    override val doc = runCatching {
        when {
            this.isOriginatedByOptOut() -> convert<PicPayDDAOptOutEventTO>(source.payload).body.source.document
            else -> convert<PicPayDDAOptInEventTO>(source.payload).body.source.document
        }
    }.getOrDefault("")
}

@JsonTypeName("DDA_OPT_OUT_FROM_CIP_WAS_APPROVED")
data class PicPayDDAEventSourceOptOutApproved(val source: PicPayDDARegisterResponseSource) : PicPayDDAEventSource() {
    override val doc = source.document
}

@JsonTypeName("DDA_OPT_OUT_FROM_CIP_WAS_REPROVED")
data class PicPayDDAEventSourceOptOutReproved(val source: PicPayDDARegisterResponseSource) : PicPayDDAEventSource() {
    override val doc = source.document
}

@JsonTypeName("DDA_OPT_OUT_WAS_PROCESSED_WITH_ERROR")
data class PicPayDDAEventSourceOptOutError(val source: PicPayDDARegisterResponseErrorSource) : PicPayDDAEventSource() {
    override val doc = try {
        val request = convert<PicPayDDAOptOutEventTO>(source.payload)
        request.body.source.document
    } catch (ex: Throwable) {
        ""
    }
}

data class DefaultPicPayDDAEventSource(val source: Map<String, Any>) : PicPayDDAEventSource() {
    override val doc = ""
}

interface PicPayDDARegisterResponse

data class PicPayDDARegisterResponseSource(
    val personType: String,
    val document: String
) : PicPayDDARegisterResponse

data class PicPayDDARegisterResponseErrorSource(
    val errors: List<PicPayDDAResponseError>,
    val payload: String
) : PicPayDDARegisterResponse

data class PicPayDDAResponseError(
    val code: String,
    val message: String
)

data class PicPayDDAEvent(
    val payer: PicPayDDAEventPayer,
    val bankSlip: PicPayDDAEventBankSlip,
    val id: String? = null,
    val type: String
)

fun PicPayDDAEvent.requestId() = id ?: eventUUID()

data class PicPayDDAEventPayer(val type: PersonType, val document: String)

data class PicPayDDAEventBankSlip(val id: String, val updatedAt: String)