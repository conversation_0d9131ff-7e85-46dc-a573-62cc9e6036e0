package ai.friday.dda.adapters.picpay

import ai.friday.dda.FileUtils
import ai.friday.dda.adapters.picpay.DdaQuery.QueryLazyLoader.BANK_SLIP_BY_CIP_ID
import ai.friday.dda.adapters.picpay.DdaQuery.QueryLazyLoader.BANK_SLIP_BY_FILTER
import ai.friday.dda.mapper

class PicPayQueryFactory {
    companion object {
        fun queryBankSlip(cipID: String): String = DdaQuery.BankSlipByCipID(cipID).asString()
        fun queryBankSlipList(filter: BankSlipsFilter): String = DdaQuery.BankSlipByFilter(filter).asString()
    }
}

sealed interface DdaVariables {
    data class CipID(val id: String) : DdaVariables
    data class Filter(val filter: BankSlipsFilter) : DdaVariables
}

sealed class DdaQuery(val query: String, val variables: DdaVariables) {
    class BankSlipByCipID(cipID: String) :
        DdaQuery(BANK_SLIP_BY_CIP_ID, DdaVariables.CipID(cipID))

    class BankSlipByFilter(filter: BankSlipsFilter) :
        DdaQuery(BANK_SLIP_BY_FILTER, DdaVariables.Filter(filter))

    fun asString(): String = mapper.writeValueAsString(this)

    object QueryLazyLoader {
        val BANK_SLIP_BY_FILTER = resourceLoad("picpay-dda/query_bankslip_by_filter.graphql")
        val BANK_SLIP_BY_CIP_ID = resourceLoad("picpay-dda/query_bankslip_by_id.graphql")

        private fun resourceLoad(filename: String) =
            FileUtils.readLocalFileAsText(filename)?.replace("\n", "")
                ?: throw IllegalStateException("$filename not found")
    }
}