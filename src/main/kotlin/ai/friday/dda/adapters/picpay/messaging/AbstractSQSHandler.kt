package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.dda.app.interfaces.MessageHandlerConfiguration
import ai.friday.dda.parallelMap
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.Async
import io.micronaut.scheduling.cron.CronExpression
import io.micronaut.tracing.annotation.NewSpan
import java.time.Duration
import java.time.format.DateTimeFormatter
import java.util.concurrent.TimeUnit
import javax.annotation.PreDestroy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.apache.commons.lang3.time.StopWatch
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.ListQueuesRequest
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.QueueAttributeName
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest
import software.amazon.awssdk.services.sqs.model.SetQueueAttributesRequest

internal const val accountIdAttributeName = "accountId"
internal const val eventTypeAttributeName = "eventType"
internal const val billTypeAttributeName = "billType"

@ConfigurationProperties("aws.sqs")
class SQSProperties @ConfigurationInject constructor(
    val sqsWaitTime: Int,
    val visibilityTimeout: Int,
    val maxNumberOfMessages: Int,
    val dlqArn: String
)

abstract class AbstractSQSHandler(
    private val amazonSQS: SqsClient,
    private val sqsProperties: SQSProperties,
    private val configuration: MessageHandlerConfiguration
) : ApplicationEventListener<ServiceReadyEvent> {
    private val logger = LoggerFactory.getLogger(AbstractSQSHandler::class.java)

    private val queueUrls = mutableMapOf<String, String>()

    private fun getQueueURL(queueName: String) = queueUrls.getOrPut(queueName) {
        amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
    }

    var isActive = false
        private set

    private val cronExpression: CronExpression? by lazy {
        configuration.timeWindowCron?.let {
            CronExpression.create(it)
        }
    }

    override fun onApplicationEvent(event: ServiceReadyEvent) {
        val markers = Markers.append("queueName", configuration.queueName)
            .and("configuration" to configuration)

        try {
            createQueueIfNotExists()
            isActive = true
            repeat(configuration.consumers) { consumerId ->
                logger.info(markers, "${configuration.queueName}-$consumerId-start")
                receiveMessages(consumerId)
            }
        } catch (e: Exception) {
            logger.error(markers, "AbstractSQSHandlerOnServiceReadyEvent", e)
            throw e
        }
    }

    @NewSpan
    @Async(TaskExecutors.MESSAGE_CONSUMER)
    open fun receiveMessages(consumerId: Int = 0) {
        val queueUrl = getQueueURL(configuration.queueName)

        val receiveRequest = ReceiveMessageRequest.builder().queueUrl(queueUrl)
            .maxNumberOfMessages(sqsProperties.maxNumberOfMessages)
            .waitTimeSeconds(sqsProperties.sqsWaitTime)
            .messageAttributeNames(accountIdAttributeName, eventTypeAttributeName, billTypeAttributeName)
            .attributeNames(QueueAttributeName.ALL)
            .build()

        while (isActive) {
            if (!isInTheRunWindow()) continue

            val messages = amazonSQS.receiveMessage(receiveRequest).messages()
            process(consumerId, messages, queueUrl)
        }
    }

    open fun process(consumerId: Int = 0, messages: List<Message>, queueUrl: String) {
        for (message in messages) {
            tryProcessMessage(message, queueUrl)
        }
    }

    protected fun tryProcessMessage(message: Message, queueUrl: String) {
        try {
            val response = handleMessage(message)
            if (response.shouldDeleteMessage) {
                amazonSQS.deleteMessage(
                    DeleteMessageRequest.builder().queueUrl(queueUrl).receiptHandle(message.receiptHandle()).build()
                )
            }
        } catch (e: Exception) {
            val response = handleError(message, e)
            if (response.shouldDeleteMessage) {
                amazonSQS.deleteMessage(
                    DeleteMessageRequest.builder().queueUrl(queueUrl).receiptHandle(message.receiptHandle()).build()
                )
            }
        }
    }

    private fun isInTheRunWindow(): Boolean {
        if (cronExpression == null) return true

        val now = getZonedDateTime()
        val nextExecution = cronExpression!!.nextTimeAfter(now)

        val duration = Duration.between(now, nextExecution)
        if (duration.toSeconds() > configuration.timeWindowToleranceInSeconds) {
            val markers = Markers.append("cron", configuration.timeWindowCron)
                .andAppend("nextExecution", nextExecution.format(DateTimeFormatter.ISO_DATE_TIME))

            runBlocking {
                logger.info(markers, "SQSHandler#Stopped")
                delay(duration.toMillis())
                logger.info(markers, "SQSHandler#Restarted")
            }
            return false
        }
        return true
    }

    abstract fun handleMessage(m: Message): SQSHandlerResponse

    abstract fun handleError(m: Message, e: Exception): SQSHandlerResponse

    override fun supports(event: ServiceReadyEvent) = true

    private fun createQueueIfNotExists() {
        val listQueuesResult =
            amazonSQS.listQueues(ListQueuesRequest.builder().queueNamePrefix(configuration.queueName).build())
        if (listQueuesResult.queueUrls().isEmpty()) {
            val createQueueResult =
                amazonSQS.createQueue(CreateQueueRequest.builder().queueName(configuration.queueName).build())
            val request = SetQueueAttributesRequest.builder().queueUrl(createQueueResult.queueUrl()).attributes(
                mapOf(
                    QueueAttributeName.VISIBILITY_TIMEOUT to sqsProperties.visibilityTimeout.toString(),
                    QueueAttributeName.REDRIVE_POLICY to "{\"maxReceiveCount\":\"60\", \"deadLetterTargetArn\":\"${sqsProperties.dlqArn}\"}"
                )
            ).build()

            amazonSQS.setQueueAttributes(request)
        }
    }

    @PreDestroy
    fun tearDown() {
        isActive = false
    }
}

class WorkersCalculator(
    minParallelism: Int,
    maxParallelism: Int,
    private val healthIndicatorTimeInMillis: Int = 5000,
    private val minHardLimit: Int = 1,
    private val maxHardLimit: Int = 10,
    private val hardDownScale: Boolean = true,
    private val queueName: String = ""
) {
    init {
        if (minParallelism < 1 || maxParallelism < 1 || minParallelism > maxParallelism) {
            throw IllegalStateException("Invalid values for maxParallelism and maxParallelism")
        }
    }

    val maxWorkers: Int by lazy { applyMaxHardLimit(maxParallelism) }
    val minWorkers: Int by lazy { applyMinHardLimit(minParallelism) }

    private var currentWorkers: Int = minWorkers
    private val computedExecutions = arrayListOf<Long>()
    private val updateWorkersAfterEach = 10

    fun currentWorkers(): Int = runCatching {
        val size = computedExecutions.size
        // TODO define this magic number

        if (size > 0 && size % updateWorkersAfterEach == 0) {
            val avgTime = computedExecutions.average()
            val delta = healthIndicatorTimeInMillis - avgTime
            val previousWorkers = currentWorkers

            val markers = Markers.appendEntries(
                mapOf(
                    "computedExecutions" to computedExecutions.toArray(),
                    "previousWorkers" to previousWorkers,
                    "average" to avgTime,
                    "delta" to delta,
                    "queue" to queueName
                )
            )

            computedExecutions.clear()

            when {
                delta > 0 -> increaseWorkers()
                else -> decreaseWorkers()
            }

            if (previousWorkers != currentWorkers) {
                logger.info(markers.andAppend("updatedWorkers", currentWorkers), "WorkersCalculator")
            }
        }

        currentWorkers
    }.getOrElse { error ->
        minWorkers.also {
            logger.error(Markers.append("computedExecutions", computedExecutions), "WorkersCalculator", error)
            computedExecutions.clear()
        }
    }

    fun computeJobStarted(): StopWatch {
        val sw = StopWatch()
        sw.start()
        return sw
    }

    fun computeJobElapsedTimeInMillis(elapsedTime: Long) {
        computedExecutions.add(elapsedTime)
    }

    // TODO avaliar aumentar/manter
    private fun increaseWorkers() {
        currentWorkers = Integer.min(applyMaxHardLimit(++currentWorkers), maxWorkers)
    }

    // TODO avaliar diminuir/manter
    private fun decreaseWorkers() {
        currentWorkers = when (hardDownScale) {
            false -> Integer.max(applyMinHardLimit(--currentWorkers), minWorkers)
            true -> minWorkers
        }
    }

    private fun applyMaxHardLimit(number: Int) = Integer.min(number, maxHardLimit)
    private fun applyMinHardLimit(number: Int) = Integer.max(applyMaxHardLimit(number), minHardLimit)

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}

abstract class AbstractParallelSQSHandler(
    amazonSQS: SqsClient,
    properties: SQSProperties,
    configuration: MessageHandlerConfiguration,
    minParallelism: Int = 1,
    maxParallelism: Int = 10,
    healthIndicatorTimeInMillis: Int = 5000
) : AbstractSQSHandler(amazonSQS, properties, configuration) {

    private val workersCalculator by lazy {
        val calculatorsList = mutableListOf<WorkersCalculator>()
        repeat(configuration.consumers) {
            calculatorsList.add(WorkersCalculator(minParallelism, maxParallelism, healthIndicatorTimeInMillis))
        }
        calculatorsList
    }

    private val autoScaleWorkers by lazy { configuration.autoScaleWorkersInParallel }

    override fun process(consumerId: Int, messages: List<Message>, queueUrl: String) {
        val calculator = workersCalculator[consumerId]

        val workers =
            if (autoScaleWorkers) calculator.currentWorkers() else calculator.minWorkers

        messages.chunked(workers).forEach { chunk ->
            runBlocking(Dispatchers.IO) {
                chunk.parallelMap {
                    val watch = calculator.computeJobStarted()
                    tryProcessMessage(it, queueUrl)
                    val elapsedTime = watch.getTime(TimeUnit.MILLISECONDS)
                    calculator.computeJobElapsedTimeInMillis(elapsedTime)
                }
            }
        }
    }
}

data class SQSHandlerResponse(val shouldDeleteMessage: Boolean)