package ai.friday.dda.adapters.picpay

import ai.friday.dda.app.AmountCalculationModel
import ai.friday.dda.app.BillStatus
import ai.friday.dda.app.DiscountData
import ai.friday.dda.app.DiscountType
import ai.friday.dda.app.Document
import ai.friday.dda.app.FichaCompensacaoType
import ai.friday.dda.app.FineType
import ai.friday.dda.app.InterestType
import ai.friday.dda.app.PaymentStatus
import ai.friday.dda.app.Recipient
import ai.friday.dda.app.interfaces.Filters
import ai.friday.dda.app.metrics.AbstractFridayCountMetrics
import ai.friday.dda.app.metrics.metricRegister
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonInclude
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

data class BankSlip(
    val acceptances: List<Acceptance>?,
    val administeredReceivingParticipantISPB: String?,
    val amount: BigDecimal,
    val barcodeNumber: String,
    val beneficiaryInformation: List<String>?,
    val calculationModel: BankSlipCalculationModel?,
    val calculations: List<Calculation>?,
    val cnab: String?,
    val currentTotalPaymentAmount: BigDecimal?,
    val discounts: List<Discount>?,
    val dispatchDate: LocalDate?,
    val documentNumber: String?,
    val expirationDate: LocalDate, // DtVencTit
    val finalBeneficiary: Person?,
    val fine: Fine?,
    val guarantorDrawer: Person?,
    val identificationNumber: String,
    val installmentNumber: Int?,
    val interest: Interest?,
    val invoices: List<Invoice>?,
    val mainReceivingParticipantISPB: String?,
    val maxAmount: BigDecimal?,
    val maxAmountType: String?,
    val minAmount: BigDecimal?,
    val minAmountType: String?,
    val negotiatedIndicator: Boolean?,
    val numberOfInstallments: Int?,
    val numberOfProtestDays: Int?,
    val originalBeneficiary: Person?,
    val ourNumberIdentifier: String?,
    val partialPaymentAmount: Int?,
    val partialPaymentAmountRegistered: Int?,
    val partialPaymentIndicator: Boolean?,
    val participantControlNumber: String?,
    val payer: Payer,
    val paymentBlockingIndicator: Boolean?,
    val paymentDeadline: LocalDate?, // DtLimPgtoTit
    val paymentSituation: PaymentSituation?,
    val paymentType: String?,
    val portfolioCode: String?,
    val rebateAmount: BigDecimal?,
    val recipientParticipantCode: String,
    val recipientParticipantISPB: String,
    val registrationCurrentReferenceNumber: String?,
    val registrationUpdateSequenceNumber: String?,
    val situation: BankSlipSituation?,
    val bankSlipSituation: JDBankSlipSituation?,
    val speciesCode: BankSlipSpeciesCode?,
    val thirdParties: List<Any>?,
    val typeOfAuthorizationForReceiptOfDivergentAmount: String?,
    val typeableBarcodeNumber: String?,
    val updatedAt: LocalDateTime?,
    val writeOffs: List<BankSlipWriteOff>?
)

data class Person(
    val document: String?,
    val name: String?,
    val tradeName: String?,
    val type: PersonType?
) {
    fun toRecipient() = Recipient(name ?: "", document)
}

data class Acceptance(
    val indicator: Boolean,
    val referenceNumber: String,
    val sequenceNumber: String
)

data class Calculation(
    val discountAmount: BigDecimal?,
    val dueDate: LocalDate?,
    val fineAmount: BigDecimal?,
    val interestAmount: BigDecimal?,
    val totalAmount: BigDecimal?
)

data class Discount(
    val amount: BigDecimal?,
    val amountType: BankSlipDiscountType,
    val moment: LocalDate?
) {

    companion object {
        fun toDiscountData(discounts: List<Discount>?): DiscountData {
            return DiscountData(
                type = BankSlipDiscountType.toDiscountType(discounts?.getOrNull(0)?.amountType),
                value1 = discounts?.getOrNull(0)?.amount,
                date1 = discounts?.getOrNull(0)?.moment,
                value2 = discounts?.getOrNull(1)?.amount,
                date2 = discounts?.getOrNull(1)?.moment,
                value3 = discounts?.getOrNull(2)?.amount,
                date3 = discounts?.getOrNull(2)?.moment
            )
        }
    }
}

data class Fine(
    val amount: BigDecimal?,
    val amountType: BankSlipFineType,
    val moment: LocalDate?
)

data class Interest(
    val code: BankSlipInterestCode,
    val moment: LocalDate?,
    val percentage: BigDecimal?
)

data class Invoice(
    val amount: BigDecimal,
    val issuedAt: LocalDate?,
    val number: String
)

data class Payer(
    val document: String,
    val name: String,
    val tradeName: String? = null,
    val type: String
)

data class PersonFilter(
    val type: PersonType,
    val document: String
)

data class BankSlipRange(
    val ini: Int,
    val fin: Int
)

data class DateFilter(
    val ini: LocalDate,
    val fin: LocalDate
) {
    companion object {
        fun from(range: Filters.DateRange) = DateFilter(range.start, range.end)
    }
}

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class BankSlipsFilter(
    val payer: PersonFilter? = null,
    val type: BankSlipTypeFilter? = null,
    val range: BankSlipRange? = null,
    val dueDate: DateFilter? = null,
    val registration: DateFilter? = null,
    val situation: BankSlipSituation? = null,
    val paymentSituation: PaymentSituation? = null
) {
    companion object {
        fun new(document: Document, dueDateRange: Filters.DateRange, page: Filters.Pagination) =
            BankSlipsFilter(
                payer = PersonFilter(PersonType.NATURAL, document.value),
                dueDate = DateFilter.from(dueDateRange),
                range = BankSlipRange(
                    (page.pageNum * page.pageSize) - (page.pageSize - 1),
                    page.pageNum * page.pageSize
                )
            )
    }
}

data class BankSlipList(
    val totalItems: Int,
    val items: List<BankSlip>
)

// Basic wrappers for deserialization
class BankSlipWrapper(val data: BankSlipContainer?)
class BankSlipContainer(val bankSlip: BankSlip?)

class BankSlipListWrapper(val data: BankSlipListContainer?)
class BankSlipListContainer(val bankSlips: BankSlipList?)

data class BankSlipWriteOff(
    val identificationNumber: String?, // NumIdentcBaixaOperac (Número Identificação Baixa)
    val currentReferenceNumber: String?, // NumRefAtlBaixaOperac (Número Referência Atual Baixa)
    val updateSequenceNumber: String?, // NumSeqAtlzBaixaOperac (Número Sequência Atualização Baixa)
    val registrationReferenceNumber: String?, // NumRefCadTitBaixaOperac (Número Referência Cadastro Título Baixa)
    val currentRegistrationReferenceNumber: String?, // NumRefAtlCadTitBaixaOperac (Número Referência Atual Cadastro Título Baixa)
    val receivingParticipantISPB: String?, // ISPBPartRecbdrBaixaOperac (ISPB Participante Recebedor Baixa)
    val receivingParticipantCode: String?, // CodPartRecbdrBaixaOperac (Código Participante Recebedor Baixa)
    val updatedAt: LocalDateTime?, // DtHrSitBaixaOperac (Data Hora Situação Baixa)
    val type: BankSlipWriteOffType?, // TpBaixaOperac (Tipo Baixa)
    val situation: OperationalWriteOffRequestSituation?, // SitReqBaixaOperac (Situação da Requisição da Baixa)
    val details: List<WriteOffDetail>?, // (Detalhe de Baixa)
    val cancellations: List<OperationalWriteOffCancellation>? // (Cancelamento de Baixa)
)

data class BankSlipOperationalWriteOff(
    val identificationNumber: String?, // NumIdentcBaixaOperac (Número Identificação Baixa Operacional)
    val currentReferenceNumber: String?, // NumRefAtlBaixaOperac (Número Referência Atual Baixa Operacional)
    val updateSequenceNumber: String?, // NumSeqAtlzBaixaOperac (Número Sequência Atualização Baixa Operacional)
    val registrationReferenceNumber: String?, // NumRefCadTitBaixaOperac (Número Referência Cadastro Título Baixa Operacional)
    val currentRegistrationReferenceNumber: String?, // NumRefAtlCadTitBaixaOperac (Número Referência Atual Cadastro Título Baixa Operacional)
    val receivingParticipantISPB: String?, // ISPBPartRecbdrBaixaOperac (ISPB Participante Recebedor Baixa Operacional)
    val receivingParticipantCode: String?, // CodPartRecbdrBaixaOperac (Código Participante Recebedor Baixa Operacional)
    val updatedAt: LocalDateTime?, // DtHrSitBaixaOperac (Data Hora Situação Baixa Operacional)
    val type: BankSlipWriteOffType?, // TpBaixaOperac (Tipo Baixa Operacional)
    val situation: OperationalWriteOffRequestSituation?, // SitReqBaixaOperac (Situação da Requisição da Baixa Operacional)
    val details: List<OperationalWriteOffDetail>?, // (Detalhe de Baixa Operacional)
    val cancellations: List<OperationalWriteOffCancellation>? // (Cancelamento de Baixa Operacional)
)

data class OperationalWriteOffDetail(
    val carrierPersonType: PersonType?, // TpPessoaPort (Tipo Pessoa Portador)
    val carrierDocument: String?, // CPFCNPJPort (PixKeyType.CPF ou CNPJ Portador)
    val carrierName: String?, // Nom_RzSocPort (Nome do Portador)
    val bankSlipReceiveAt: LocalDateTime?, // DtHrRecbtTit (Data e Hora do Recebimento do Título)
    val operationalWriteOffProcessedAt: LocalDateTime?, // DtHrProcBaixaOperac (Data Hora Processamento Baixa Operacional)
    val operationalWriteOffDetailBankSlipAmount: BigDecimal?, // VlrBaixaOperacTit (Valor Baixa Operacional Título)
    val operationalWriteOffBarCodeNumber: String?, // NumCodBarrasBaixaOperac (Número Código Barras Baixa Operacional)
    val operationalWriteOffPaymentChannel: PaymentChannel?, // CanPgtoBaixaOperac (Canal Pagamento Baixa Operacional)
    val operationalWriteOffPaymentMethod: PaymentMethod?, // MeioPgtoBaixaOperac (Meio Pagamento Baixa Operacional)
    val contingencyOperationIndicator: Boolean? // IndrOpContg (Indicador Operação Contingência)
)

data class WriteOffDetail(
    val carrierPersonType: PersonType?, // TpPessoaPort (Tipo Pessoa Portador)
    val carrierDocument: String?, // CPFCNPJPort (PixKeyType.CPF ou CNPJ Portador)
    val carrierName: String?, // Nom_RzSocPort (Nome do Portador)
    val bankSlipReceiveAt: LocalDateTime?, // DtHrRecbtTit (Data e Hora do Recebimento do Título)
    val writeOffProcessedAt: LocalDateTime?, // DtHrProcBaixaOperac (Data Hora Processamento Baixa Operacional)
    val writeOffDetailBankSlipAmount: BigDecimal?, // VlrBaixaOperacTit (Valor Baixa Operacional Título)
    val writeOffBarCodeNumber: String?, // NumCodBarrasBaixaOperac (Número Código Barras Baixa Operacional)
    val writeOffPaymentChannel: PaymentChannel?, // CanPgtoBaixaOperac (Canal Pagamento Baixa Operacional)
    val writeOffPaymentMethod: PaymentMethod?, // MeioPgtoBaixaOperac (Meio Pagamento Baixa Operacional)
    val contingencyOperationIndicator: Boolean? // IndrOpContg (Indicador Operação Contingência)
)

data class OperationalWriteOffCancellation(
    val canceledAt: String? // DtHrCancelBaixaOperac (Data Hora Cancelamento Baixa Operacional)
)

data class EffectiveWriteOff(
    val identificationNumber: String?, // NumIdentcBaixaEft (Número Identificação Baixa Efetiva)
    val currentReferenceNumber: String?, // NumRefAtlBaixaEft (Número Referência Atual Baixa Efetiva)
    val updateSequenceNumber: String?, // NumSeqAtlzBaixaEft (Número Sequência Atualização Baixa Efetiva)
    val operationalWriteOffIdentificationNumber: String?, // NumIdentcBaixaOperac (Número Identificação Baixa Operacional Baixa Efetiva)
    val type: EffectiveWriteOffType?, // TpBaixaEft (Tipo Baixa Efetiva)
    val receivingParticipantISPB: String?, // ISPBPartRecbdrBaixaEft (ISPB Participante Recebedor Baixa Efetiva)
    val receivingParticipantCode: String?, // CodPartRecbdrBaixaEft (Código Participante Recebedor Baixa Efetiva)
    val processingDateAndTime: LocalDateTime?, // DtHrProcBaixaEft Data Hora Processamento Baixa
    val valueOfTheEffectiveWriteOffOfTheBankSlip: BigDecimal?, // VlrBaixaEftTit (Valor Baixa Efetiva Título, Utiliza separador decimal "ponto". Ex.: 1599.25)
    val barCode: String?, // NumCodBarrasBaixaEft (Número Código de Barras Baixa Efetiva)
    val paymentChannel: PaymentChannel?, // CanPgtoBaixaEft (Canal de Pagamento)
    val paymentMethod: PaymentMethod?, // MeioPgtoBaixaEft (Meio de Pagamento)
    val momentOfEffectiveWriteOffStatus: LocalDateTime? // DtHrSitBaixaEft (Data Hora Situação Baixa Efetiva)
)

// ENUMS

enum class EffectiveWriteOffType {
    EFFECTIVE_FULL_INTERBANK_WRITE_OFF, // Baixa Efetiva Integral Interbancária
    INTRA_BANK_FULL_EFFECTIVE_WRITE_OFF, // Baixa Efetiva Integral Intrabancária
    FULL_EFFECTIVE_WRITE_OFF_AT_ASSIGNOR, // Baixa Efetiva Integral por Solicitação do Cedente
    FULL_EFFECTIVE_WRITE_OFF_BY_SUBMISSION_TO_PROTEST, // Baixa Efetiva Integral por envio para protesto
    FULL_EFFECTIVE_RETIREMENT_DUE_TO_EXPIRATION_OF_TERM, // Baixa Efetiva Integral por decurso de prazo
    EFFECTIVE_PARTIAL_INTRA_BANK_WRITE_OFF, // Baixa Efetiva Parcial Intrabancária
    EFFECTIVE_PARTIAL_INTERBANK_WRITE_OFF, // Baixa Efetiva Parcial Interbancária
    EFFECTIVE_WRITE_OFF_AT_THE_REQUEST // Baixa Efetiva por solicitação da Instituição Destinatária
}

enum class BankSlipCalculationModel(val model: AmountCalculationModel) {
    RECEIVING_INSTITUTION_CALCULATES_EXPIRING_BANK_SLIPS_AND_EXPIRED_BANK_SLIPS(AmountCalculationModel.ANYONE),
    RECIPIENT_INSTITUTION_CALCULATES_EXPIRED_BANK_SLIPS_AND_RECEIVING_INSTITUTION_CALCULATES_EXPIRING_BANK_SLIPS(
        AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE
    ),
    RECIPIENT_INSTITUTION_CALCULATES_EXPIRING_BANK_SLIPS_AND_EXPIRED_BANK_SLIPS(AmountCalculationModel.BENEFICIARY_ONLY),
    CIP_RECEIVING_INSTITUTION_CALCULATES_BALANCE_FOR_PARTIAL_PAYMENT_AND_EXPIRING_BANK_SLIPS_AND_EXPIRED_BANK_SLIPS(
        AmountCalculationModel.ON_DEMAND
    );

    companion object {
        private val mappedSources = BankSlipCalculationModel.values().associateBy(BankSlipCalculationModel::model)
        private val mappedTargets = BankSlipCalculationModel.values().associateBy(BankSlipCalculationModel::name)

        fun of(type: AmountCalculationModel) = mappedSources[type]
        fun toAmountCalculationModel(model: BankSlipCalculationModel?): AmountCalculationModel =
            model?.let { mappedTargets[it.name]?.model } ?: AmountCalculationModel.UNKNOWN
    }
}

enum class BankSlipSpeciesCode(val type: FichaCompensacaoType) {
    CH_CHECK(FichaCompensacaoType.CH_CHEQUE),
    DM_TRADE_DUPLICATE(FichaCompensacaoType.DM_DUPLICATA_MERCANTIL),
    DMI_DUPLICATE_MERCANTILE_INDICATION(FichaCompensacaoType.DMI_DUPLICATA_MERCANTIL_INDICACAO),
    DS_DUPLICATE_SERVICE(FichaCompensacaoType.DS_DUPLICATA_DE_SERVICO),
    DSI_DUPLICATE_SERVICE_INDICATION(FichaCompensacaoType.DSI_DUPLICATA_DE_SERVICO_INDICACAO),
    DR_RURAL_DUPLICATE(FichaCompensacaoType.DR_DUPLICATA_RURAL),
    LC_BILL_OF_EXCHANGE(FichaCompensacaoType.LC_LETRA_DE_CAMBIO),
    NCC_CREDIT_NOTE_COMMERCIAL(FichaCompensacaoType.NCC_NOTA_DE_CREDITO_COMERCIAL),
    NCE_EXPORT_CREDIT_NOTE(FichaCompensacaoType.NCE_NOTA_DE_CREDITO_EXPORTACAO),
    NCI_INDUSTRIAL_CREDIT_NOTE(FichaCompensacaoType.NCI_NOTA_DE_CREDITO_INDUSTRIAL),
    NCR_RURAL_CREDIT_NOTE(FichaCompensacaoType.NCR_NOTA_DE_CREDITO_RURAL),
    NP_PROMISSORY_NOTE(FichaCompensacaoType.NP_NOTA_PROMISSORIA),
    NPR_RURAL_PROMISSORY_NOTE(FichaCompensacaoType.NPR_NOTA_PROMISSORIA_RURAL),
    TM_COMMERCIAL_TRIPLICATE(FichaCompensacaoType.TM_TRIPLICATA_MERCANTIL),
    TS_TRIPLICATE_SERVICE(FichaCompensacaoType.TS_TRIPLICATA_DE_SERVICO),
    NS_INSURANCE_NOTE(FichaCompensacaoType.NS_NOTA_DE_SEGURO),
    RC_RECEIPT(FichaCompensacaoType.RC_RECIBO),
    FAT_BLOCK(FichaCompensacaoType.FAT_BLOQUETO),
    ND_DEBIT_NOTE(FichaCompensacaoType.ND_NOTA_DE_DEBITO),
    AP_INSURANCE_POLICY(FichaCompensacaoType.AP_APOLICE_DE_SEGURO),
    ME_SCHOOL_TUITION(FichaCompensacaoType.ME_MENSALIDADE_ESCOLAR),
    PC_CONSORTIUM_INSTALLMENT(FichaCompensacaoType.PC_PARCELA_DE_CONSORCIO),
    NF_INVOICE(FichaCompensacaoType.NF_NOTA_FISCAL),
    DD_DEBT_DOCUMENT(FichaCompensacaoType.DD_DOCUMENTO_DE_DIVIDA),
    RURAL_PRODUCT_BILL(FichaCompensacaoType.CEDULA_DE_PRODUTO_RURAL),
    WARRANT(FichaCompensacaoType.WARRANT),
    ACTIVE_STATE_DEBT(FichaCompensacaoType.DIVIDA_ATIVA_DE_ESTADO),
    MUNICIPAL_ACTIVE_DEBT(FichaCompensacaoType.DIVIDA_ATIVA_DE_MUNICIPIO),
    UNION_ACTIVE_DEBT(FichaCompensacaoType.DIVIDA_ATIVA_DA_UNIAO),
    CONDOMINIUM_CHARGES(FichaCompensacaoType.ENCARGOS_CONDOMINIAIS),
    CREDIT_CARD(FichaCompensacaoType.CARTAO_DE_CREDITO),
    PROPOSED_BANK_SLIP(FichaCompensacaoType.BOLETO_PROPOSTA),
    DEPOSIT_CONTRIBUTION_BANK_SLIP(FichaCompensacaoType.DEPOSITO_E_APORTE),
    OTHERS(FichaCompensacaoType.OUTROS);

    companion object {
        private val mappedSources = BankSlipSpeciesCode.values().associateBy(BankSlipSpeciesCode::type)
        private val mappedTargets = BankSlipSpeciesCode.values().associateBy(BankSlipSpeciesCode::name)

        fun of(type: FichaCompensacaoType) = mappedSources[type]
        fun toFichaCompensacaoType(model: BankSlipSpeciesCode?): FichaCompensacaoType =
            model?.let { mappedTargets[it.name]?.type } ?: FichaCompensacaoType.OUTROS
    }
}

enum class BankSlipFineType(val type: FineType) {
    FIXED_VALUE(FineType.VALUE),
    PERCENTAGE(FineType.PERCENT),
    EXEMPT(FineType.FREE);

    companion object {
        private val mappedSources = BankSlipFineType.values().associateBy(BankSlipFineType::type)
        private val mappedTargets = BankSlipFineType.values().associateBy(BankSlipFineType::name)

        fun of(type: FineType) = mappedSources[type]
        fun toFineType(model: BankSlipFineType?): FineType =
            model?.let { mappedTargets[it.name]?.type } ?: FineType.UNKNOWN
    }
}

enum class PersonType { NATURAL, LEGAL }

enum class BankSlipInterestCode(val type: InterestType) {
    VALUE_CURRENT_DAYS(InterestType.VALUE),
    PERCENTAGE_PER_DAY_CALENDAR_DAYS(InterestType.PERCENT_BY_DAY),
    PERCENTAGE_PER_MONTH_CALENDAR_DAYS(InterestType.PERCENT_BY_MONTH),
    PERCENTAGE_PER_YEAR_CALENDAR_DAYS(InterestType.PERCENT_BY_YEAR),
    EXEMPT(InterestType.FREE),
    VALUE_WORKING_DAY(InterestType.VALUE_WORKING_DAYS),
    PERCENTAGE_PER_DAY_WORKING_DAYS(InterestType.PERCENT_BY_DAY_WORKING_DAYS),
    PERCENTAGE_PER_MONTH_WORKING_DAYS(InterestType.PERCENT_BY_MONTH_WORKING_DAYS),
    PERCENTAGE_PER_YEAR_WORKING_DAYS(InterestType.PERCENT_BY_YEAR_WORKING_DAYS);

    companion object {
        private val mappedSources = BankSlipInterestCode.values().associateBy(BankSlipInterestCode::type)
        private val mappedTargets = BankSlipInterestCode.values().associateBy(BankSlipInterestCode::name)

        fun of(type: InterestType) = mappedSources[type]
        fun toInterestType(model: BankSlipInterestCode?): InterestType =
            model?.let { mappedTargets[it.name]?.type } ?: InterestType.UNKNOWN
    }
}

enum class BankSlipDiscountType(val type: DiscountType) {
    EXEMPT(DiscountType.FREE),
    FIXED_AMOUNT_UNTIL_THE_INFORMED_DATE(DiscountType.FIXED_UNTIL_DATE),
    PERCENTAGE_UP_TO_THE_DATE_REPORTED(DiscountType.PERCENT_UNTIL_DATE),
    AMOUNT_IN_ADVANCE_ON_A_CALENDAR_DAY(DiscountType.VALUE_BY_DAY),
    VALUE_IN_ADVANCE_WORKING_DAY(DiscountType.VALUE_BY_WORKING_DAY),
    PERCENTAGE_IN_ADVANCE_CALENDAR_DAY(DiscountType.PERCENT_BY_DAY),
    PERCENTAGE_IN_ADVANCE_WORKING_DAY(DiscountType.PERCENT_BY_WORKING_DAY);

    companion object {
        private val mappedSources = BankSlipDiscountType.values().associateBy(BankSlipDiscountType::type)
        private val mappedTargets = BankSlipDiscountType.values().associateBy(BankSlipDiscountType::name)

        fun of(type: DiscountType) = mappedSources[type]
        fun toDiscountType(model: BankSlipDiscountType?): DiscountType =
            model?.let { mappedTargets[it.name]?.type } ?: DiscountType.UNKNOWN
    }
}

enum class BankSlipTypeFilter {
    OWN,
    THIRD_PARTY
}

enum class BankSlipSituation(val type: BillStatus? = null) {
    OPEN(BillStatus.ABERTO),
    EFFECTIVE_WRITE_OFF_AT_THE_REQUEST_OF_THE_BENEFICIARY(BillStatus.BAIXA_EFETIVA_REALIZADA),
    EFFECTIVE_WRITE_OFF_BY_INTRA_BANK_SETTLEMENT(BillStatus.BAIXA_EFETIVA_REALIZADA),
    EFFECTIVE_WRITE_OFF_BY_INTERBANK_SETTLEMENT(BillStatus.BAIXA_EFETIVA_REALIZADA),
    EFFECTIVE_WRITE_OFF_DUE_TO_LAPSE_OF_TERM(BillStatus.BAIXA_EFETIVA_REALIZADA),
    EFFECTIVE_WRITE_OFF_BY_SENDING_FOR_PROTEST(BillStatus.BAIXA_EFETIVA_REALIZADA),
    EFFECTIVE_WRITE_OFF_AT_REQUEST_OF_THE_RECIPIENT_INSTITUTION(BillStatus.BAIXA_EFETIVA_REALIZADA);

    companion object {
        private val mappedTargets = BankSlipSituation.values().associateBy(BankSlipSituation::name)

        fun BankSlipSituation?.toBillStatus(): BillStatus? = this?.let { BankSlipSituation.mappedTargets[it.name]?.type }
    }
}

class InvalidEnumValue : AbstractFridayCountMetrics()

enum class JDBankSlipSituation(val type: BillStatus? = null) {
    OPEN(BillStatus.ABERTO),
    WRITE_OFF_AT_THE_REQUEST_OF_THE_BENEFICIARY(BillStatus.BAIXA_EFETIVA_REALIZADA),
    WRITE_OFF_BY_INTRA_BANK_SETTLEMENT(BillStatus.BAIXA_EFETIVA_REALIZADA),
    WRITE_OFF_BY_INTERBANK_SETTLEMENT(BillStatus.BAIXA_EFETIVA_REALIZADA),
    WRITE_OFF_DUE_TO_LAPSE_OF_TERM(BillStatus.BAIXA_EFETIVA_REALIZADA),
    WRITE_OFF_BY_SENDING_FOR_PROTEST(BillStatus.BAIXA_EFETIVA_REALIZADA),
    WRITE_OFF_AT_REQUEST_OF_THE_RECIPIENT_INSTITUTION(BillStatus.BAIXA_EFETIVA_REALIZADA);

    companion object {
        private val mappedTargets = JDBankSlipSituation.values().associateBy(JDBankSlipSituation::name)

        fun JDBankSlipSituation?.toBillStatus(): BillStatus? = this?.let { mappedTargets[it.name]?.type }

        @JvmStatic
        @JsonCreator
        fun create(value: String?): JDBankSlipSituation? =
            value?.uppercase()?.let {
                mappedTargets[it] ?: run {
                    metricRegister(InvalidEnumValue(), "type" to "JDBankSlipSituation", "value" to value)
                    null
                }
            }
    }
}

enum class PaymentSituation(val type: PaymentStatus = PaymentStatus.OUTRO) {
    BANK_SLIP_ALREADY_WRITTEN_OFF(PaymentStatus.BAIXA_REALIZADA), // CIP 1
    BANK_SLIP_BLOCKED_FOR_PAYMENT(PaymentStatus.BLOQUEADO), // CIP 2
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_BENEFICIARY_CUSTOMER_UNSUITABLE_AT_THE_INSTITUTION_ISSUING_THE_BANKSLIP,
    BANK_SLIP_FOUND_IN_THE_BASE_AND_BENEFICIARY_CUSTOMER_WITHOUT_REGISTRATION,
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_BENEFICIARY_CUSTOMER_UNDER_ANALYSIS_AT_THE_BANK_ISSUING_INSTITUTION,
    BANK_SLIP_EXCEEDED_THE_LIMIT_FOR_PARTIAL_PAYMENTS,
    FULL_OPERATING_WRITE_OFF_ALREADY_REGISTERED(PaymentStatus.PAGAMENTO_REGISTRADO_EM_BAIXA_OPERACIONAL), // CIP 7
    BANK_SLIP_EXCEEDED_BALANCE_AMOUNT_FOR_PARTIAL_PAYMENT_FOR_CALCULATION_MODEL_TYPE_FOUR,
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_UNSUITABLE_BENEFICIARY_CUSTOMER_IN_AN_INSTITUTION_OTHER_THAN_THE_ISSUER,
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_BENEFICIARY_CUSTOMER_UNDER_ANALYSIS_AT_AN_INSTITUTION_OTHER_THAN_THE_ISSUER,
    BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_ELIGIBLE_BENEFICIARY_CUSTOMER(PaymentStatus.ENCONTRADO_E_BENEFICIARIO_APTO); // CIP 12

    companion object {
        private val mappedTargets = PaymentSituation.values().associateBy(PaymentSituation::name)
        fun toPaymentStatusType(situation: PaymentSituation?): PaymentStatus =
            situation?.let { mappedTargets[it.name]?.type } ?: PaymentStatus.OUTRO
    }
}

enum class BankSlipWriteOffType {
    WRITE_OFF_INTERBANK, // Baixa Operacional Integral Interbancária
    WRITE_OFF_INTRA_BANK, // Baixa Operacional Integral Intrabancária
    WRITE_OFF_INTRA_PARTIAL_INTRA_BANK // Baixa Operacional Parcial Intrabancária
}

enum class OperationalWriteOffRequestSituation {
    PENDING, // Pendente
    SENT, // Enviado
    REJECTED, // Rejeitado
    EFFECTIVE // Efetivado
}

enum class PaymentChannel {
    AGENCY, // Agências – Postos tradicionais
    SELF_SERVICE, // Terminal de Auto-atendimento
    INTERNET, // Internet (home / office banking)
    BANKING_CORRESPONDENT, // Correspondente bancário
    CALL_CENTER, // Central de atendimento (Call Center)
    ELECTRONIC_FILE, // Arquivo Eletrônico
    DDA // DDA
}

enum class PaymentMethod {
    IN_CASH, // Espécie
    DEBIT, // Débito em conta
    CREDIT, // Cartão de crédito
    CHECK // Cheque
}