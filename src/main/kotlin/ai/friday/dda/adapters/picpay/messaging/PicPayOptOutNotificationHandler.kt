package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.picpay.OptOutResult
import ai.friday.dda.adapters.picpay.PicPayRegisterAdapter
import ai.friday.dda.andAppend
import ai.friday.dda.app.interfaces.MessageHandlerConfiguration
import ai.friday.dda.app.interfaces.ddaOptOutNotification
import ai.friday.dda.convert
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "aws.sqs.handler.dda-opt-out-notification.enabled", value = "true")
)
open class PicPayOptOutNotificationHandler(
    amazonSQS: SqsClient,
    properties: SQSProperties,
    @Named(ddaOptOutNotification) private val configuration: MessageHandlerConfiguration,
    private val picPayRegisterAdapter: PicPayRegisterAdapter
) : AbstractParallelSQSHandler(amazonSQS, properties, configuration, maxParallelism = 10) {
    companion object {
        private val LOG = LoggerFactory.getLogger(this::class.java)
    }

    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val markers = Markers.append("body", m.body())

        val event = convert<PicPayOptOutNotificationTO>(m.body())

        val result = picPayRegisterAdapter.sendOptOutNotification(consumerId = event.consumerId)

        markers.andAppend("result", result).andAppend("event", event)

        LOG.info(markers, "PicPayOptOutNotificationHandler#handleMessage")

        return when (result) {
            OptOutResult.Success,
            OptOutResult.AccountAlreadyProcessed,
            OptOutResult.AccountNotFound,
            OptOutResult.BadRequest -> SQSHandlerResponse(shouldDeleteMessage = true)

            OptOutResult.Failure,
            OptOutResult.UnknownResponse,
            OptOutResult.UnprocessableEntity -> SQSHandlerResponse(shouldDeleteMessage = false)
        }
    }

    override fun handleError(
        m: Message,
        e: Exception
    ): SQSHandlerResponse {
        val markers = Markers.append("body", m.body())
        LOG.error(markers, "PicPayOptOutNotificationHandler#handleMessage")

        return SQSHandlerResponse(shouldDeleteMessage = false)
    }
}

data class PicPayOptOutNotificationTO(val consumerId: String)