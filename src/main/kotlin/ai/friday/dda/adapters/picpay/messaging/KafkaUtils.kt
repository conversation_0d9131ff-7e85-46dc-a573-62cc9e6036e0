package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.aws.SQSMessagePublisher
import ai.friday.dda.andAppend
import ai.friday.dda.app.interfaces.MessageHandlerConfiguration
import ai.friday.dda.app.interfaces.kafkaRouterBean
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.configuration.kafka.exceptions.KafkaListenerException
import io.micronaut.configuration.kafka.exceptions.KafkaListenerExceptionHandler
import io.micronaut.context.annotation.Requires
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

data class DLQConfiguration(
    val sqsMessagePublisher: SQSMessagePublisher,
    val queueName: String
)

open class CustomKafkaListenerExceptionHandler(
    private val dlqConfiguration: DLQConfiguration? = null
) : KafkaListenerExceptionHandler {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun handle(exception: KafkaListenerException?) {
        val record = exception?.consumerRecord?.get()

        dlqConfiguration?.let { config ->
            record?.value()?.let {
                config.sqsMessagePublisher.sendMessage(config.queueName, it)
            }
        }

        val markers = Markers.append("key", record?.key())
            .andAppend("value", record?.value())
            .andAppend("offset", record?.offset())
            .andAppend("partition", record?.partition())
            .andAppend("cause", exception?.message)

        logger.error(markers, javaClass.simpleName)
    }
}

@Singleton
@Requires(env = [PIC_PAY_ENV])
open class SQSKafkaRouter(
    private val producer: PicPayKafkaPublisher,
    cli: SqsClient,
    properties: SQSProperties,
    @Named(kafkaRouterBean) private val configuration: MessageHandlerConfiguration,
) : AbstractSQSHandler(cli, properties, configuration) {
    private val logger = LoggerFactory.getLogger(SQSKafkaRouter::class.java)

    override fun handleMessage(m: Message): SQSHandlerResponse {
        val markers = Markers.append("queue_message", m.body())
        try {
            logger.info(markers, javaClass.simpleName)
            val event = jacksonObjectMapper().readValue(m.body(), KafkaRouterTO::class.java)

            producer.send(topic = event.topic, event = event.body)
        } catch (err: Exception) {
            logger.error(markers, javaClass.simpleName, err)
        }
        return SQSHandlerResponse(true)
    }

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        val markers = Markers.append("message", m.body())
        logger.error(markers, javaClass.simpleName, e)
        return SQSHandlerResponse(true)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class KafkaRouterTO(
    val topic: String,
    val body: Map<String, Any>,
)

object KafkaConstants {
    const val KAFKA_APPLICATION_NAME = "friday"
    const val DDA_RECEIVED_EVENT_NAME = "DDA_BANKSLIP_NOTIFICATION_FROM_FRIDAY_WAS_RECEIVED"

    object Topics {
        const val DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME = "baas-banking-dda-bankslip-notifications"
        const val DDA_BANKSLIP_NOTIFICATIONS_STATUS_TOPIC_NAME = "baas-banking-dda-bankslip-notifications-status"
        const val DDA_INBOUND_TOPIC_NAME = "baas-banking-dda-inbound"
        const val DDA_OUTBOUND_TOPIC_NAME = "baas-banking-dda-outbound"
    }
}

fun eventUUID() = "dda-service-${System.currentTimeMillis()}-${UUID.randomUUID()}"