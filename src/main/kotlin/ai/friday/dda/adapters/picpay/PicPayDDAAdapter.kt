package ai.friday.dda.adapters.picpay

import ai.friday.dda.Err
import ai.friday.dda.ServerError
import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.BillRegisterDataListResponse
import ai.friday.dda.app.DdaItem
import ai.friday.dda.app.Document
import ai.friday.dda.app.UnexpectedResponse
import ai.friday.dda.app.interfaces.DDAProviderServiceV2
import ai.friday.dda.app.interfaces.DDARequestFilter
import ai.friday.dda.app.metrics.DdaReceiveRtt
import ai.friday.dda.app.metrics.metricRegister
import ai.friday.dda.log
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Secondary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import jakarta.inject.Singleton
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val bankSlipPath = "/graphql"

@Secondary
@Singleton
class PicPayDDAAdapter(
    @Client("dda-finder") private val httpClient: HttpClient,
    private val converter: PicPayDDAAdapterConverter
) : DDAProviderServiceV2 {
    companion object {
        private val LOG = LoggerFactory.getLogger(this::class.java)
    }

    override fun findByCipId(filter: DDARequestFilter.CIPFilter): Either<Err, BillRegisterData> =
        with(filter) { bankSlipBy(cipID) }

    override fun findByFilter(filter: DDARequestFilter.RangeFilter): Either<Err, BillRegisterDataListResponse> =
        with(filter) { bankSlipList(BankSlipsFilter.new(document, dateRange, page)) }

    private fun bankSlipBy(cipID: String): Either<Err, BillRegisterData> {
        val markers = log("cip_id" to cipID, "method" to "bankSlipBy")

        val result =
            runCatching {
                val payload = PicPayQueryFactory.queryBankSlip(cipID)
                val request = HttpRequest.POST(bankSlipPath, payload).contentType(MediaType.APPLICATION_JSON)
                httpClient.toBlocking().exchange(request, Argument.of(BankSlipWrapper::class.java), Argument.STRING)
            }

        val response =
            result.getOrElse {
                if (it is HttpClientResponseException) {
                    markers.and(
                        "body" to it.response.getBody(String::class.java),
                        "status" to it.response.status,
                        "message" to it.message
                    )
                }

                LOG.error(markers, "PicPayDDAAdapter")
                return ServerError(err = it).left()
            }

        val bankSlip = response.body()?.data?.bankSlip

        bankSlip.let { b -> // We decided to log and count write offs grouping them by situation
            b?.writeOffs?.groupingBy { it.situation }?.eachCount().let { result ->
                markers.andAppend("writeOffs", result)
            }

            b?.rtt().run {
                markers.andAppend("rtt", this)
                if (this != null) metricRegister(DdaReceiveRtt(this))
            }
        }

        LOG.info(
            markers.and("status" to response.status, "body" to response.getBody(String::class.java)),
            "PicPayDDAAdapter"
        )

        return bankSlip?.let {
            converter.toDDARegister(it).right()
        } ?: UnexpectedResponse.left()
    }

    private fun bankSlipList(filter: BankSlipsFilter): Either<Err, BillRegisterDataListResponse> {
        val markers = Markers.appendEntries(mapOf("filter" to filter, "method" to "bankSlipList"))

        val result =
            runCatching {
                val payload = PicPayQueryFactory.queryBankSlipList(filter)
                val request = HttpRequest.POST(bankSlipPath, payload).contentType(MediaType.APPLICATION_JSON)
                httpClient.toBlocking().exchange(request, Argument.of(BankSlipListWrapper::class.java), Argument.STRING)
            }

        logResult(result, markers)

        val response =
            result.getOrElse {
                return ServerError(err = it).left()
            }

        return response.body.get().data?.bankSlips?.let {
            BillRegisterDataListResponse(
                totalItems = it.totalItems,
                items = it.items.map { item -> converter.toDDARegister(item) }.toMutableList()
            ).right()
        } ?: BillRegisterDataListResponse().right()
    }

    private fun logResult(
        result: Result<HttpResponse<BankSlipListWrapper>>,
        markers: LogstashMarker
    ): LogstashMarker {
        result.fold(
            onSuccess = {
                // Yeah, I know. We decided to log and count write offs grouping them by situation
                result.getOrNull()?.body()?.data?.bankSlips?.items
                    ?.asSequence()?.mapNotNull { it.writeOffs }?.flatten()
                    ?.groupingBy { it.situation }?.eachCount().let { result ->
                        markers.andAppend("writeOffs", result)
                    }

                LOG.info(
                    markers.andAppend("body", result.getOrNull()?.getBody(String::class.java))
                        .andAppend("status", result.getOrNull()?.status),
                    "PicPayDDAAdapter"
                )
            },
            onFailure = {
                if (it is HttpClientResponseException) {
                    markers
                        .andAppend("body", it.response.body)
                        .andAppend("status", it.response.status)
                        .andAppend("message", it.message)
                }

                LOG.error(markers, "PicPayDDAAdapter", it)
            }
        )

        return markers
    }

    override fun getBills(
        dueDate: LocalDate,
        document: Document?
    ): List<DdaItem> {
        TODO("Not yet implemented")
    }

    override fun add(documents: List<Document>) {
        TODO("Not yet implemented")
    }

    override fun remove(document: Document) {
        TODO("Not yet implemented")
    }
}