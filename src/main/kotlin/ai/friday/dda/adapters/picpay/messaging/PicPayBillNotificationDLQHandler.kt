package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.NotFoundErr
import ai.friday.dda.app.dda.DDAEventService
import ai.friday.dda.app.interfaces.MessageHandlerConfiguration
import ai.friday.dda.app.interfaces.ddaBillNotificationDlqBean
import ai.friday.dda.app.metrics.metricRegister
import ai.friday.dda.convert
import arrow.core.getOrHandle
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "aws.sqs.handler.dda-bill-notification-dlq.enabled", value = "true")
)
open class PicPayBillNotificationDLQHandler(
    amazonSQS: SqsClient,
    properties: SQSProperties,
    @Named(ddaBillNotificationDlqBean) private val configuration: MessageHandlerConfiguration,
    private val service: DDAEventService
) : AbstractSQSHandler(amazonSQS, properties, configuration) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val markers = Markers.append("body", m.body())

        val event = convert<PicPayDDAEvent>(m.body())

        val result = service.handleEvent(cipID = event.bankSlip.id, requestId = event.requestId(), type = event.type)

        markers.andAppend("result", result).andAppend("event", event)

        result.getOrHandle {
            markers.and("reason" to it.toString())
            when (it) {
                is NotFoundErr -> {
                    metricRegister(PicPayDDABillNotificationMetric(), "result" to "NOT_FOUND", "dlq" to "true")
                    logger.warn(markers, "PicPayBillNotificationDLQHandler#notFound")
                    return SQSHandlerResponse(shouldDeleteMessage = true)
                }

                else -> {
                    metricRegister(PicPayDDABillNotificationMetric(), "result" to "FAILED", "reason" to it.toString(), "dlq" to "true")
                    logger.error(markers, "PicPayBillNotificationDLQHandler", it.ex)
                    return SQSHandlerResponse(shouldDeleteMessage = false)
                }
            }
        }

        metricRegister(PicPayDDABillNotificationMetric(), "result" to "SUCCESS", "dlq" to "true")

        logger.info(markers, "PicPayBillNotificationDLQHandler")
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        m: Message,
        e: Exception
    ): SQSHandlerResponse {
        logger.error(Markers.append("body", m.body()), "PicPayBillNotificationDLQHandler")
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }
}