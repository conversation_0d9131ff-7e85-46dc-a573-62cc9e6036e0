package ai.friday.dda.adapters.aws

import ai.friday.dda.andAppend
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.dda.app.interfaces.FileProcessResponseTO
import ai.friday.dda.app.interfaces.ObjectRepository
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.io.InputStream
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class S3FileProcessor(private val objectRepository: ObjectRepository) {
    private val logger = LoggerFactory.getLogger(S3FileProcessor::class.java)

    private val dowloadedDirectory = "downloaded"

    fun processDownloadedFiles(bucketName: String, contextName: String, processor: (InputStream) -> FileProcessResponseTO) {
        logger.info(Markers.append("status", "iniciando"), contextName)

        val unprocessedFiles =
            objectRepository.listObjectKeys(bucketName = bucketName, directoryKey = dowloadedDirectory)
        if (unprocessedFiles.isEmpty()) {
            logger.warn(Markers.append("warning", "NO_FILES_FOUND"), contextName)
        }
        for (file in unprocessedFiles) {
            processDownloadedFile(file, contextName, bucketName, processor)
        }
        logger.info(Markers.append("status", "finalizando"), contextName)
    }

    @NewSpan
    open fun processDownloadedFile(
        file: String,
        contextName: String,
        bucketName: String,
        processor: (InputStream) -> FileProcessResponseTO
    ) {
        val markers = Markers.append("filename", file)

        logger.info(markers, contextName)

        val loadedFile = objectRepository.loadObject(bucketName = bucketName, key = file)
        val result = processStream(loadedFile, processor)

        val today = getLocalDate()
        val s3FolderName = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"))

        result.onSuccess {
            objectRepository.moveObject(
                bucketName = bucketName,
                fromKey = file,
                toKey = "processed/$s3FolderName/${file.substringAfterLast("/")}"
            )
            logger.info(
                markers.andAppend("result", "success")
                    .andAppend("totalItems", it.totalItems)
                    .andAppend("processedItems", it.processedItems),
                contextName
            )
        }.onFailure {
            objectRepository.moveObject(
                bucketName = bucketName,
                fromKey = file,
                toKey = "errors/${file.substringAfterLast("/")}"
            )
            logger.error(markers.andAppend("result", "error"), contextName, it)
        }
    }

    private fun processStream(inputStream: InputStream, processor: (InputStream) -> FileProcessResponseTO) =
        runCatching { processor(inputStream) }
}