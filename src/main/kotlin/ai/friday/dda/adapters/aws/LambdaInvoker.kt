package ai.friday.dda.adapters.aws

import ai.friday.dda.write
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.lambda.LambdaClient
import software.amazon.awssdk.services.lambda.model.InvokeRequest
import software.amazon.awssdk.services.lambda.model.InvokeResponse

@Singleton
class LambdaInvoker(private val lambdaClient: LambdaClient) {
    private val logger = LoggerFactory.getLogger(LambdaInvoker::class.java)

    fun invoke(functionName: String, payload: Any): Result<String> {
        val markers = Markers.append("functionName", functionName)

        return runCatching {
            val payloadJson = write(payload)

            val invokeRequest = InvokeRequest.builder()
                .functionName(functionName)
                .payload(SdkBytes.fromUtf8String(payloadJson))
                .build()

            val response: InvokeResponse = lambdaClient.invoke(invokeRequest)

            if (response.statusCode() in 200..299) {
                response.payload().asUtf8String()
            } else {
                logger.error(markers.and(Markers.append("statusCode", response.statusCode())), "LambdaInvoker")
                throw IllegalStateException("Lambda returned ${response.statusCode()}")
            }
        }
    }
}