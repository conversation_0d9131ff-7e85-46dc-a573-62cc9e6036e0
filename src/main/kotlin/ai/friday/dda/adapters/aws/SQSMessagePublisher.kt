package ai.friday.dda.adapters.aws

import ai.friday.dda.Err
import ai.friday.dda.ServerError
import ai.friday.dda.andAppend
import ai.friday.dda.app.QueueMessage
import ai.friday.dda.app.QueueMessageBatch
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.write
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import net.logstash.logback.marker.Markers.appendArray
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.BatchRequestTooLongException
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequest
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequestEntry
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

@Singleton
class SQSMessagePublisher(private val amazonSQS: SqsClient) : MessagePublisher {

    private val queueUrls = mutableMapOf<String, String>()

    private fun getQueueURL(queueName: String): String {
        return queueUrls.getOrPut(queueName) {
            amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
        }
    }

    override fun sendMessage(queue: String, body: Any, delay: Int?) =
        sendMessage(QueueMessage(queueName = queue, write(body), delay))

    override fun sendMessage(queueMessage: QueueMessage): Either<Err, Unit> {
        val queueUrl = getQueueURL(queueMessage.queueName)

        return doSendMessage(queueUrl, queueMessage.jsonObject, queueMessage.delaySeconds)
    }

    override fun sendMessageBatch(
        messageBatch: QueueMessageBatch
    ) {
        if (messageBatch.messages.isEmpty()) return

        val queueURL = getQueueURL(messageBatch.queueName)

        val chunks = messageBatch.messages.chunked(BATCH_SIZE)

        for (chunk in chunks) {
            try {
                sendAsBatch(chunk, queueURL)
            } catch (e: BatchRequestTooLongException) {
                LOG.warn("SQSMessagePublisher#sendMessageBatch", e)
                sendAsSingle(chunk, queueURL)
            }
        }
    }

    private fun sendAsBatch(chunk: List<String>, queueURL: String) {
        val foo = chunk.mapIndexed { index, message ->
            LOG.info(append("queueMessage", message), "SQSMessagePublisher")
            SendMessageBatchRequestEntry.builder().id(index.toString())
                .messageBody(message)
                .build()
        }

        val batchRequest = SendMessageBatchRequest.builder().queueUrl(queueURL).entries(foo).build()
        val response = amazonSQS.sendMessageBatch(batchRequest)

        if (response.hasFailed()) {
            LOG.error(
                append("failedMessagesSize", response.failed().size).and(
                    appendArray(
                        "faileMessages",
                        response.failed()
                    )
                ),
                "SQSMessagePublisher"
            )
        }

        LOG.info(append("queuedMessages", response.successful().size), "SQSMessagePublisher")
    }

    private fun sendAsSingle(chunk: List<String>, queueURL: String) {
        chunk.forEach {
            doSendMessage(queueUrl = queueURL, messageBody = it, delay = null)
        }
    }

    private fun doSendMessage(queueUrl: String, messageBody: String, delay: Int?): Either<Err, Unit> {
        val sendMessageRequestBuilder =
            SendMessageRequest.builder().queueUrl(queueUrl).messageBody(messageBody)

        delay?.let { delaySeconds -> sendMessageRequestBuilder.delaySeconds(delaySeconds) }

        val sendMessageRequest = sendMessageRequestBuilder.build()

        val markers = append("queueUrl", queueUrl)
            .andAppend("messageBody", messageBody)
            .andAppend("delay", delay)
            .andAppend("messageRequest", sendMessageRequest)

        return try {
            val response = amazonSQS.sendMessage(sendMessageRequest)
            LOG.info(markers.andAppend("messageId", response.messageId()), "SQSMessagePublisher")
            Unit.right()
        } catch (ex: Exception) {
            LOG.error(markers, "SQSMessagePublisher", ex)
            ServerError(err = ex).left()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSMessagePublisher::class.java)
        private const val BATCH_SIZE = 10
    }
}