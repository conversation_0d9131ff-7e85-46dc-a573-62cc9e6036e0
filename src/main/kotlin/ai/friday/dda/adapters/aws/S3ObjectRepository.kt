package ai.friday.dda.adapters.aws

import ai.friday.dda.app.brazilTimeZone
import ai.friday.dda.app.interfaces.ObjectRepository
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.time.ZonedDateTime
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response
import software.amazon.awssdk.services.s3.model.PutObjectRequest

@Singleton
class S3ObjectRepository(
    private val s3Client: S3Client
) : ObjectRepository {

    override fun listObjectKeys(bucketName: String, directoryKey: String): List<String> {
        val listing: ListObjectsV2Response = s3Client.listObjectsV2 {
            it.bucket(bucketName).prefix(directoryKey)
        }

        return listing.contents().map { it.key() }
    }

    override fun listObjectLastModified(bucketName: String, directoryKey: String): List<ZonedDateTime> {
        val listing: ListObjectsV2Response = s3Client.listObjectsV2 {
            it.bucket(bucketName).prefix(directoryKey)
        }

        return listing.contents().map { ZonedDateTime.ofInstant(it.lastModified(), brazilTimeZone) }
    }

    override fun loadObject(bucketName: String, key: String): InputStream {
        return s3Client.getObject {
            it.bucket(bucketName).key(key)
        }
    }

    override fun moveObject(bucketName: String, fromKey: String, toKey: String) {
        s3Client.copyObject {
            it.sourceBucket(bucketName).sourceKey(fromKey)
                .destinationBucket(bucketName).destinationKey(toKey)
        }

        s3Client.deleteObject {
            it.bucket(bucketName).key(fromKey)
        }
    }

    override fun uploadObject(bucketName: String, key: String, stream: InputStream) {
        val buffer = stream.use { it.readAllBytes() }
        val req: RequestBody = RequestBody.fromInputStream(
            ByteArrayInputStream(buffer),
            buffer.size.toLong()
        )
        val reqObject = PutObjectRequest.builder().bucket(bucketName).key(key).build()
        s3Client.putObject(reqObject, req)
    }
}