package ai.friday.dda.adapters.configuration

import ai.friday.dda.app.tenant.ResponseConfiguration
import ai.friday.dda.app.tenant.TenantConfiguration
import ai.friday.dda.app.tenant.TenantName
import ai.friday.dda.app.tenant.TenantProtocol
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@EachBean(TenantConfiguration::class)
annotation class EachTenant

@EachProperty("tenants")
class TenantConfigurationMicronaut @ConfigurationInject constructor(
    @param:Parameter private val name: String,
    val bucket: String,
    protocol: TenantProtocol,
    configuration: Map<String, Any>,
) {
    val tenantName = TenantName(name.uppercase())

    val configuration: ResponseConfiguration = when (protocol) {

        TenantProtocol.HTTP -> ResponseConfiguration.HttpConfiguration(
            configuration["username"] as String,
            configuration["password"] as String
        )

        TenantProtocol.MESSAGE -> ResponseConfiguration.MessageConfiguration(
            configuration["queue-name"] as String
        )
    }
}