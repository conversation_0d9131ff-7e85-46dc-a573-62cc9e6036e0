package ai.friday.dda.adapters.lock

import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.dda.app.interfaces.InternalLock
import java.time.Duration
import net.javacrumbs.shedlock.core.LockConfiguration
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.core.SimpleLock

class InternalLockProvider(
    private val lockProvider: LockProvider,
    private val lockConfigurationConfiguration: InternalLockConfiguration,
) : InternalLock {
    override fun acquireLock(
        lockName: String,
        minDuration: Duration?,
        maxDuration: Duration?,
        simultaneousLock: Int,
    ): SimpleLock? {
        repeat(times = simultaneousLock) { index ->
            val suffix = if (index > 0) "#${index + 1}" else ""

            val lock = lockProvider.lock(
                LockConfiguration(
                    getZonedDateTime().toInstant(),
                    "${lockConfigurationConfiguration.prefix}$lockName$suffix",
                    maxDuration ?: lockConfigurationConfiguration.maxDuration,
                    minDuration ?: lockConfigurationConfiguration.minDuration,
                ),
            ).orElse(null)

            if (lock != null) return lock
        }
        return null
    }

    override fun waitForAcquireLock(lockName: String): SimpleLock {
        while (true) {
            val lock = lockProvider.lock(
                LockConfiguration(
                    getZonedDateTime().toInstant(),
                    "${lockConfigurationConfiguration.prefix}$lockName",
                    lockConfigurationConfiguration.maxDuration,
                    lockConfigurationConfiguration.minDuration,
                ),
            ).orElse(null)

            if (lock != null) return lock
        }
    }
}