package ai.friday.dda.adapters.lock

import ai.friday.dda.adapters.dynamodb.SHEDLOCK_TABLE_NAME
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Parameter
import jakarta.inject.Singleton
import java.time.Duration
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.dynamodb2.DynamoDBLockProvider
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

@Factory
class LockFactory {
    @Singleton
    fun dbLockProvider(cli: DynamoDbClient): LockProvider = DynamoDBLockProvider(cli, SHEDLOCK_TABLE_NAME)

    @EachBean(InternalLockConfiguration::class)
    fun lockProvider(dbLockProvider: LockProvider, configuration: InternalLockConfiguration) =
        InternalLockProvider(dbLockProvider, configuration)
}

@EachProperty("lock.configuration")
class InternalLockConfiguration @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val maxDuration: Duration,
    val minDuration: Duration,
    val prefix: String,
)