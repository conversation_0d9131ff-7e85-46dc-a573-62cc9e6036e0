package ai.friday.dda.adapters.jobs

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.aws.S3FileProcessor
import ai.friday.dda.adapters.multicom.PGPStreamProcessor
import ai.friday.dda.app.concessionariadireto.MulticomService
import ai.friday.dda.app.interfaces.FileProcessResponseTO
import ai.friday.dda.app.job.AbstractJob
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.io.InputStream
import java.util.Base64

@Singleton
@Requires(env = [PIC_PAY_ENV])
open class MulticomBillsJob(
    private val multicomService: MulticomService,
    private val pgpStreamProcessor: PGPStreamProcessor,
    private val configuration: MulticomBillsJobConfiguration,
    private val processor: S3FileProcessor,
    @Property(name = "integrations.concessionaria.multicom.bucketName") private val bucketName: String
) : AbstractJob(cron = "10,40 * * * *") {

    @NewSpan
    override fun execute() {
        processor.processDownloadedFiles(bucketName, "MulticomBillsJob") { doProcessStream(it) }

        multicomService.checkExpiredBills()
    }

    private fun doProcessStream(inputStream: InputStream): FileProcessResponseTO {
        val decodedKey = Base64.getDecoder().decode(configuration.key)

        val decryptedInputStream = pgpStreamProcessor.decryptStream(
            encryptedStream = inputStream,
            keyStream = decodedKey.inputStream(),
            password = configuration.passphrase
        )
        return multicomService.processFile(decryptedInputStream)
    }
}

@ConfigurationProperties("integrations.concessionaria.multicom.pgp")
class MulticomBillsJobConfiguration @ConfigurationInject constructor(
    val key: String,
    val passphrase: String
)