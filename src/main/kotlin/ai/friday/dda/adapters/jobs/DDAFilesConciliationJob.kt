package ai.friday.dda.adapters.jobs

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.configuration.EachTenant
import ai.friday.dda.andAppend
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.dda.app.brazilTimeZone
import ai.friday.dda.app.interfaces.ObjectRepository
import ai.friday.dda.app.tenant.TenantConfiguration
import io.micronaut.context.annotation.Requires
import io.micronaut.scheduling.annotation.Scheduled
import io.micronaut.tracing.annotation.NewSpan
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@EachTenant
@Requires(notEnv = [PIC_PAY_ENV])
open class DDAFilesConciliationJob(
    private val objectRepository: ObjectRepository,
    private val configuration: TenantConfiguration,
) {
    private val logger = LoggerFactory.getLogger(DDAFilesConciliationJob::class.java)

    private val processedDirectoryName = "processed"

    @NewSpan
    @Scheduled(cron = "30 00-20 * * *", zoneId = "America/Sao_Paulo")
    open fun process() {
        val mostRecentFileDates = objectRepository.listObjectLastModified(
            bucketName = configuration.bucket,
            directoryKey = "$processedDirectoryName/${getLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))}"
        )

        if (mostRecentFileDates.isEmpty()) {
            alarm(ChronoUnit.MINUTES.between(getLocalDate().atStartOfDay(brazilTimeZone), getZonedDateTime()))
            return
        }

        val mostRecentFilesDate = mostRecentFileDates.maxOf { it }
        val minutesAfterLastFile = ChronoUnit.MINUTES.between(mostRecentFilesDate, getZonedDateTime())
        if (mostRecentFilesDate.isBefore(getZonedDateTime().minusHours(1))) {
            alarm(minutesAfterLastFile)
            return
        }

        logger.info(append("missingFile", "false").andAppend("minutesMissingFile", minutesAfterLastFile), "DDAFilesConciliationJob#process")
    }

    fun alarm(minutes: Long) {
        logger.error(
            append("missingFile", "true")
                .andAppend("minutesMissingFile", minutes),
            "DDAFilesConciliationJob#process",
        )
    }
}