package ai.friday.dda.adapters.jobs

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.picpay.messaging.DDAOptOutRequestSource
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptOutEventBody
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptOutEventTO
import ai.friday.dda.adapters.picpay.messaging.PicPayKafkaPublisher
import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.dda.DDAProvider
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDAStatus
import ai.friday.dda.app.interfaces.DDARepository
import ai.friday.dda.app.job.AbstractJob
import ai.friday.dda.measure
import ai.friday.dda.parallelMap
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "features.dda.deregister.enabled", value = "true")
)
open class PicPayDDADeregisterJob(
    private val ddaRepository: DDARepository,
    private val picPayKafkaPublisher: PicPayKafkaPublisher,
    @Property(name = "features.dda.deregister.cron") cron: String
) : AbstractJob(cron = cron) {
    private val logger = LoggerFactory.getLogger(PicPayDDADeregisterJob::class.java)

    @NewSpan
    override fun execute() {
        val ddaRegisters = ddaRepository.findByStatus(DDAStatus.PENDING_CLOSE)
            .filter { it.provider == DDAProvider.PICPAY }

        val markers = Markers.append("size", ddaRegisters.size)

        logger.info(markers.andAppend("status", "started"), "PicPayDDADeregisterJob")

        val (_, executionElapsedTime) = measure { ddaRegisters.forEach(::process) }

        logger.info(
            markers.and("executionElapsedTime" to executionElapsedTime, "status" to "finished"),
            "PicPayDDADeregisterJob"
        )
    }

    open fun enhancedOptOut(rowsPerExecution: Int = 1500) {
        val ddaRegisters = ddaRepository.findByStatus(DDAStatus.PENDING_CLOSE)
            .filter { it.provider == DDAProvider.PICPAY }
            .take(rowsPerExecution)

        val markers = Markers.append("size", ddaRegisters.size)

        logger.info(markers.andAppend("status", "started"), "PicPayDDADeregisterJob")

        ddaRegisters.chunked(150).forEach { chunk ->
            val (_, executionElapsedTime) = measure {
                runBlocking(Dispatchers.IO) {
                    chunk.parallelMap { process(it) }
                }
            }

            logger.info(
                markers.and("executionElapsedTime" to executionElapsedTime, "status" to "finished"),
                "PicPayDDADeregisterJob"
            )
        }
    }

    private fun process(ddaRegister: DDARegister) {
        val uuid = UUID.randomUUID().toString() + System.currentTimeMillis()
        val marker = Markers.append("document", ddaRegister.document)
            .and("accountId" to ddaRegister.accountId)
            .and("ddaStatus" to ddaRegister.status)
            .and("cid" to uuid)

        try {
            val latestDDARegister = ddaRepository.findByDocument(ddaRegister.document)
            if (latestDDARegister != null && latestDDARegister.accountId != ddaRegister.accountId) {
                logger.info(
                    marker.and("status" to "alreadyExistsANewDDARegister", "event" to "optout"),
                    "PicPayDDADeregisterJob"
                )
                ddaRepository.save(ddaRegister.copy(status = DDAStatus.CLOSED))
                return
            }

            picPayKafkaPublisher.send(uuid, event = ddaRegister.toPicPayOptOutRequest())
            ddaRepository.save(ddaRegister.copy(status = DDAStatus.CLOSING))

            logger.info(marker.and("status" to "sent", "event" to "optout"), "PicPayDDADeregisterJob")
        } catch (ex: Exception) {
            marker.andAppend("status", "error")
            logger.error(marker, "PicPayDDADeregisterJob", ex)
        }
    }

    private fun DDARegister.toPicPayOptOutRequest() = PicPayDDAOptOutEventTO(
        body = PicPayDDAOptOutEventBody(source = DDAOptOutRequestSource(document = this.document))
    )
}