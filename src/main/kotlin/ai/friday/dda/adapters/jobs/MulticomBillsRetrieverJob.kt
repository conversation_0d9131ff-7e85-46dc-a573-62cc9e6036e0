package ai.friday.dda.adapters.jobs

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.multicom.FTPMulticomAdapter
import ai.friday.dda.andAppend
import ai.friday.dda.app.interfaces.ObjectRepository
import ai.friday.dda.app.job.AbstractJob
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Requires(env = [PIC_PAY_ENV])
@Singleton
open class MulticomBillsRetrieverJob(
    private val objectRepository: ObjectRepository,
    private val ftpMulticomAdapter: FTPMulticomAdapter,
    @Property(name = "integrations.concessionaria.multicom.bucketName") private val bucketName: String
) : AbstractJob(cron = "5,35 * * * *") {
    private val logger = LoggerFactory.getLogger(MulticomBillsRetrieverJob::class.java)

    private val dowloadedDirectory = "downloaded"

    @NewSpan
    override fun execute() {
        logger.info(Markers.append("status", "iniciando"), "retrieveFilesJob")
        try {
            val ftpWorkingDirectory = "Outbound"
            val files = ftpMulticomAdapter.listFiles("/$ftpWorkingDirectory")

            logger.info(Markers.append("filename", files), "retrieveFilesJob")

            files.forEach {
                val markers = Markers.append("currentFile", it)
                val stream = ftpMulticomAdapter.downloadFile("/$ftpWorkingDirectory/$it")
                objectRepository.uploadObject(bucketName, "$dowloadedDirectory/$it", stream)
                ftpMulticomAdapter.deleteFile("/$ftpWorkingDirectory/$it")
                logger.info(markers.andAppend("result", "success"), "retrieveFilesJob")
            }
            logger.info(Markers.append("status", "finalizando"), "retrieveFilesJob")
        } catch (e: Exception) {
            logger.error("retrieveFilesJob", e)
        }
    }
}