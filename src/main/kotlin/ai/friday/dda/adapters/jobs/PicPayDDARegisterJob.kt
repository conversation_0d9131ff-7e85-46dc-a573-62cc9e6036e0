package ai.friday.dda.adapters.jobs

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.picpay.messaging.DDAOptInRequestAccount
import ai.friday.dda.adapters.picpay.messaging.DDAOptInRequestSource
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptInEventBody
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptInEventTO
import ai.friday.dda.adapters.picpay.messaging.PicPayKafkaPublisher
import ai.friday.dda.and
import ai.friday.dda.andAppend
import ai.friday.dda.app.dda.DDAProvider
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDAStatus
import ai.friday.dda.app.interfaces.DDARepository
import ai.friday.dda.app.job.AbstractJob
import ai.friday.dda.asCoroutineOtelTracingDispatcher
import ai.friday.dda.measure
import ai.friday.dda.parallelMap
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration
import java.util.UUID
import java.util.concurrent.ExecutorService
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requirements(
    Requires(env = [PIC_PAY_ENV]),
    Requires(property = "features.dda.register.enabled", value = "true")
)
open class PicPayDDARegisterJob(
    private val ddaRepository: DDARepository,
    private val picPayKafkaPublisher: PicPayKafkaPublisher,
    @Named("jobExecutor") val executor: ExecutorService
) : AbstractJob(fixedDelay = Duration.ofMinutes(1)) {
    private val logger = LoggerFactory.getLogger(PicPayDDARegisterJob::class.java)
    private val chunkSize: Int = 30
    private val rowsPerExecution: Int = 1500

    @NewSpan
    override fun execute() {
        val pendingDDARegisters =
            ddaRepository.findByStatus(DDAStatus.PENDING)
                .filter { it.provider == DDAProvider.PICPAY }
                .take(rowsPerExecution).toMutableList()

        if (pendingDDARegisters.size < rowsPerExecution) {
            val delta = rowsPerExecution - pendingDDARegisters.size
            val migratingDDARegisters =
                ddaRepository
                    .findByStatus(DDAStatus.PENDING_MIGRATION_OPTIN)
                    .take(delta)

            pendingDDARegisters.addAll(migratingDDARegisters)
        }

        if (pendingDDARegisters.isEmpty()) return

        val markers = Markers.append("size", pendingDDARegisters.size)
        val iteration = AtomicInteger()

        logger.info(markers, "PicPayDDARegisterJob")
        val (_, executionElapsedTime) =
            measure {
                pendingDDARegisters.chunked(chunkSize).forEach { chunk ->
                    val (_, elapsedTime) = measure { process(chunk) }
                    val i = iteration.incrementAndGet()

                    logger.info(
                        markers.and(
                            "chunkElapsedTime" to elapsedTime,
                            "iteration" to i,
                            "progress" to i * chunkSize
                        ),
                        "PicPayDDARegisterJob"
                    )
                }
            }
        logger.info(markers.andAppend("executionElapsedTime", executionElapsedTime), "PicPayDDARegisterJob")
    }

    private fun process(pendingDDARegisters: List<DDARegister>) =
        runBlocking(executor.asCoroutineOtelTracingDispatcher()) {
            pendingDDARegisters.parallelMap {
                val uuid = UUID.randomUUID().toString() + System.currentTimeMillis()
                val marker =
                    Markers.append("document", it.document)
                        .and("accountId" to it.accountId)
                        .and("ddaStatus" to it.status)
                        .and("cid" to uuid)

                try {
                    val newDDAStatus =
                        when (it.status) {
                            DDAStatus.PENDING -> DDAStatus.REQUESTING
                            DDAStatus.PENDING_MIGRATION_OPTIN -> DDAStatus.MIGRATING_OPTIN
                            else -> throw IllegalStateException("unexpected pending status")
                        }

                    picPayKafkaPublisher.send(uuid, event = it.toPicPayOptInRequest())
                    ddaRepository.save(it.copy(status = newDDAStatus))

                    logger.info(marker.and("status" to "sent", "event" to "optin"), "PicPayDDARegisterJob")
                } catch (ex: Exception) {
                    marker.andAppend("status", "error")
                    logger.error(marker, "PicPayDDARegisterJob", ex)
                }
            }
        }

    private fun DDARegister.toPicPayOptInRequest() =
        PicPayDDAOptInEventTO(
            body =
            PicPayDDAOptInEventBody(
                event = "DDA_OPT_IN_FROM_PICPAY_WAS_MADE",
                source =
                DDAOptInRequestSource(
                    personType = "NATURAL",
                    document = this.document,
                    accounts = listOf(DDAOptInRequestAccount())
                )
            )
        )
}