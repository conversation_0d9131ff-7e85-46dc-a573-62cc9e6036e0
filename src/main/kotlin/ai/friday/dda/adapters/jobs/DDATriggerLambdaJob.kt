package ai.friday.dda.adapters.jobs

import ai.friday.dda.adapters.aws.LambdaInvoker
import ai.friday.dda.adapters.configuration.EachTenant
import ai.friday.dda.app.job.AbstractJob
import ai.friday.dda.app.tenant.TenantConfiguration
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import java.time.Duration
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val lambdaFunctionName = "dda-files"

private val jobFixedDelay = Duration.ofMinutes(10)

@Requires(property = "features.dda.lambda.enabled", value = "true")
@EachTenant
open class DDATriggerLambdaJob(
    private val configuration: TenantConfiguration,
    private val lambdaInvoker: LambdaInvoker,
) : AbstractJob(
    fixedDelay = jobFixedDelay,
    tenantName = configuration.tenantName
) {
    private val logger = LoggerFactory.getLogger(DDATriggerLambdaJob::class.java)

    @NewSpan
    override fun execute() {
        val tenantName = configuration.tenantName.value
        val markers = Markers.append("tenant", tenantName)

        val payload = mapOf("environment" to tenantName)

        lambdaInvoker.invoke(lambdaFunctionName, payload)
            .onSuccess { response ->
                logger.info(
                    markers.and(Markers.append("response", response)),
                    "DDATriggerLambdaJob"
                )
            }.onFailure { error ->
                logger.error(
                    markers.and(Markers.append("error", error.toString())),
                    "DDATriggerLambdaJob", error
                )
            }
    }
}