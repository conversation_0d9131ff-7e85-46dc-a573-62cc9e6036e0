package ai.friday.dda.adapters.dynamodb

import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional

abstract class AbstractAsyncDynamoDAO<ENTITY>(cli: DynamoDbEnhancedAsyncClient) : DynamoDAO<ENTITY> {
    private var table: DynamoDbAsyncTable<ENTITY>

    init {
        val (tableName, type) = this.args()

        table = cli.table(tableName, TableSchema.fromBean(type))
    }

    fun getByPrimaryKey(partitionKey: String) = this.getByPrimaryKeyAndSortKey(partitionKey, null)

    fun getByPrimaryKeyAndSortKey(partitionKey: String, sortKey: String?): Mono<ENTITY> =
        table.getItem {
            it.key { key ->
                key.partitionValue(partitionKey)
                if (sortKey != null) key.sortValue(sortKey)
            }
        }.toMono()

    fun <OUTPUT> findAll(partitionKey: String, converter: (ENTITY) -> OUTPUT) =
        this.findByPartitionKeyAndSortKey(partitionKey, null, converter)

    fun <OUTPUT> findByPartitionKeyAndSortKey(partitionKey: String, sortKey: String?, converter: (ENTITY) -> OUTPUT) =
        Flux.from(
            table.query(
                QueryConditional.keyEqualTo {
                    it.partitionValue(partitionKey)
                    if (sortKey != null) it.sortValue(sortKey)
                }
            ).items()
        ).collectList().map { it.toList() }.map { it.map(converter) }.block() ?: emptyList()

    fun save(item: ENTITY, ignoreNulls: Boolean? = false): Mono<ENTITY> =
        table.updateItem {
            it.item(item)
            it.ignoreNulls(ignoreNulls)
        }.toMono()

    fun delete(partitionKey: String, sortKey: String): Mono<ENTITY> =
        table.deleteItem(Key.builder().partitionValue(partitionKey).sortValue(sortKey).build()).toMono()
}