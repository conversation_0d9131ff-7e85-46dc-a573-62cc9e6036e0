package ai.friday.dda.adapters.dynamodb

import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItem
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItemSaveResult
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItemStatus
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoProvider
import ai.friday.dda.app.Document
import ai.friday.dda.app.bill.BarCode
import ai.friday.dda.app.interfaces.ConcessionariaDiretoRepository
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ConcessionariaDiretoIndexPartitionKey = "CONCESSIONARIA_DIRETO_REGISTER"

@Singleton
class ConcessionariaDiretoDbRepository(private val dynamoDbDAO: ConcessionariaDiretoDynamoDAO) :
    ConcessionariaDiretoRepository {
    override fun save(concessionariaDiretoItem: ConcessionariaDiretoItem): ConcessionariaDiretoItemSaveResult {
        val response = dynamoDbDAO.saveWithResponse(concessionariaDiretoItem.toEntity())
        return if (response.attributes() == null) {
            ConcessionariaDiretoItemSaveResult.CREATED
        } else {
            ConcessionariaDiretoItemSaveResult.UPDATED
        }
    }

    override fun find(barCode: BarCode, dueDate: LocalDate): ConcessionariaDiretoItem? {
        return dynamoDbDAO.findByPrimaryKey(ConcessionariaDiretoEntityPK(barCode, dueDate))
            ?.toConcessionariaDiretoItem()
    }

    override fun findLastSeenBillsSince(lastSeen: ZonedDateTime): List<ConcessionariaDiretoItem> {
        return dynamoDbDAO.findLessThanOnIndex(
            index = GlobalSecondaryIndex.GSIndex1,
            partitionKey = buildIndex1PartitionKey(ConcessionariaDiretoItemStatus.ACTIVE),
            sortKey = lastSeen.format(DateTimeFormatter.ISO_DATE_TIME)
        ).map { it.toConcessionariaDiretoItem() }
    }
}

internal fun ConcessionariaDiretoItem.toEntityPK() = ConcessionariaDiretoEntityPK(
    barCode = barCode,
    dueDate = dueDate,
)

internal fun ConcessionariaDiretoItem.toEntity(): ConcessionariaDiretoEntity {
    val concessionariaDiretoItem = this
    val primaryKey = toEntityPK()
    return ConcessionariaDiretoEntity().apply {
        this.partitionKey = primaryKey.partitionKey
        this.rangeKey = primaryKey.sortKey
        this.digitableBarCode = concessionariaDiretoItem.barCode.digitable
        this.dueDate = concessionariaDiretoItem.dueDate.format(DateTimeFormatter.ISO_DATE)
        this.document = concessionariaDiretoItem.document.value
        this.lastSeen = concessionariaDiretoItem.lastSeen.format(DateTimeFormatter.ISO_DATE_TIME)
        this.inactivityCounter = concessionariaDiretoItem.inactivityCounter
        this.provider = concessionariaDiretoItem.provider.name
        this.providerBillId = concessionariaDiretoItem.providerBillId
        this.description = concessionariaDiretoItem.description
        this.amount = concessionariaDiretoItem.amount
        this.status = concessionariaDiretoItem.status
        this.index1HashKey = buildIndex1PartitionKey(concessionariaDiretoItem.status)
        this.index1RangeKey = lastSeen
    }
}

private fun ConcessionariaDiretoEntity.toConcessionariaDiretoItem() = ConcessionariaDiretoItem(
    amount = this.amount,
    barCode = BarCode.ofDigitable(this.digitableBarCode),
    document = Document(this.document),
    dueDate = LocalDate.parse(this.dueDate, DateTimeFormatter.ISO_DATE),
    description = this.description,
    provider = ConcessionariaDiretoProvider.valueOf(this.provider),
    providerBillId = this.providerBillId,
    status = this.status,
    inactivityCounter = this.inactivityCounter,
    lastSeen = ZonedDateTime.parse(this.lastSeen, DateTimeFormatter.ISO_DATE_TIME)
)

private fun buildIndex1PartitionKey(status: ConcessionariaDiretoItemStatus) =
    "$ConcessionariaDiretoIndexPartitionKey#${status.name}"

class ConcessionariaDiretoEntityPK(barCode: BarCode, dueDate: LocalDate) : AbstractKey<ConcessionariaDiretoEntity>(
    partitionKey = barCode.contractCode(),
    sortKey = dueDate.format(DateTimeFormatter.ISO_DATE)
)

@DynamoDbBean
class ConcessionariaDiretoEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = DDA_SERVICE_PARTITION_KEY)
    lateinit var partitionKey: String // contractCode

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = DDA_SERVICE_RANGE_KEY)
    lateinit var rangeKey: String // dueDate

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "DigitableBarcode")
    lateinit var digitableBarCode: String

    @get:DynamoDbAttribute(value = "LastSeen")
    lateinit var lastSeen: String

    @get:DynamoDbAttribute(value = "InactivityCounter")
    var inactivityCounter: Long = 0

    @get:DynamoDbAttribute(value = "DueDate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Description")
    lateinit var description: String

    @get:DynamoDbAttribute(value = "Provider")
    lateinit var provider: String

    @get:DynamoDbAttribute(value = "ProviderBillId")
    lateinit var providerBillId: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: ConcessionariaDiretoItemStatus

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var index1HashKey: String // CONCESSIONARIA_DIRETO_REGISTER + status

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var index1RangeKey: String // lastSeen
}