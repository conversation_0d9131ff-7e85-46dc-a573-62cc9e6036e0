package ai.friday.dda.adapters.dynamodb

import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.dda.app.account.AccountId
import ai.friday.dda.app.dda.DDAProvider
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDAStatus
import ai.friday.dda.app.interfaces.DDARepository
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val DDAIndexPartitionKey = "DDA_REGISTER"

@Singleton
class DDADbRepository(private val dynamoDbDAO: DDADynamoDAO) : DDARepository {

    override fun save(ddaRegister: DDARegister): DDARegister {
        dynamoDbDAO.save(ddaRegister.toEntity())
        return ddaRegister
    }

    override fun find(accountId: AccountId): DDARegister? {
        return dynamoDbDAO.findByPrimaryKey(DDAEntityPK(accountId))?.toDDARegister()
    }

    override fun findByStatus(status: DDAStatus): List<DDARegister> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndex.GSIndex1, DDAIndexPartitionKey, status.name)
            .map { it.toDDARegister() }
    }

    override fun findByDocument(document: String): DDARegister? {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndex.GSIndex2, DDAIndexPartitionKey, document)
            .map { it.toDDARegister() }
            .maxByOrNull { it.created }
    }
}

fun DDARegister.toEntity(): DDAEntity {
    val ddaRegister = this
    return DDAEntity().apply {
        this.primaryKey = ddaRegister.accountId.value
        this.scanKey = DDAIndexPartitionKey
        this.accountId = ddaRegister.accountId.value
        this.document = ddaRegister.document
        this.created = ddaRegister.created.format(DateTimeFormatter.ISO_DATE_TIME)
        this.lastUpdated = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        this.status = ddaRegister.status
        this.index1HashKey = DDAIndexPartitionKey
        this.index1RangeKey = ddaRegister.status.name
        this.lastSuccessfulExecution = ddaRegister.lastSuccessfullExecution?.format(DateTimeFormatter.ISO_DATE_TIME)
        this.migrated = ddaRegister.migrated?.format(DateTimeFormatter.ISO_DATE_TIME)
        this.provider = ddaRegister.provider
        this.index2HashKey = DDAIndexPartitionKey
        this.index2RangeKey = ddaRegister.document
    }
}

private fun DDAEntity.toDDARegister() = DDARegister(
    accountId = AccountId(this.accountId),
    document = this.document,
    created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
    lastUpdated = ZonedDateTime.parse(this.lastUpdated, DateTimeFormatter.ISO_DATE_TIME),
    lastSuccessfullExecution = this.lastSuccessfulExecution?.let {
        ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME)
    },
    migrated = this.migrated?.let {
        ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME)
    },
    status = this.status,
    provider = this.provider
)

class DDAEntityPK(accountId: AccountId) : AbstractKey<DDAEntity>(
    partitionKey = accountId.value,
    sortKey = DDAIndexPartitionKey
)

@DynamoDbBean
class DDAEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = PARTITION_KEY)
    lateinit var primaryKey: String // ACCOUNT-ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = RANGE_KEY)
    lateinit var scanKey: String // DDA

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "LastUpdated")
    lateinit var lastUpdated: String

    @get:DynamoDbAttribute(value = "LastSuccessfullExecution")
    var lastSuccessfulExecution: String? = null

    @get:DynamoDbAttribute(value = "MigratedAt")
    var migrated: String? = null

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: DDAStatus

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // DDA

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String

    @get:DynamoDbAttribute(value = "Provider")
    var provider: DDAProvider = DDAProvider.ARBI

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var index2HashKey: String // DDA

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var index2RangeKey: String
}