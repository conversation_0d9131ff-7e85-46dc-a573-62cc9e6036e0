package ai.friday.dda.adapters.dynamodb

import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

@Factory
class AmazonDynamoDBFactory {
    @Singleton
    fun dynamoDbEnhancedClient(cli: DynamoDbClient): DynamoDbEnhancedClient {
        return DynamoDbEnhancedClient.builder().dynamoDbClient(cli).build()
    }
}