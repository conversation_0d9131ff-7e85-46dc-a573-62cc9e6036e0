package ai.friday.dda.adapters.dynamodb

const val BILL_PAYMENT_TABLE_NAME = "Via1-BillPayment"

const val PARTITION_KEY = "PrimaryKey"
const val RANGE_KEY = "ScanKey"

const val DDA_SERVICE_TABLE_NAME = "Via1-DDA-Service"
const val DDA_SERVICE_PARTITION_KEY = "PartitionKey"
const val DDA_SERVICE_RANGE_KEY = "RangeKey"

const val SHEDLOCK_TABLE_NAME = "Shedlock"

enum class GlobalSecondaryIndex {
    GSIndex1, GSIndex2, GSIndex3
}