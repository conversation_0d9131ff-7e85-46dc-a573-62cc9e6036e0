package ai.friday.dda.adapters.arbi

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class DDABillTO(
    val numidentcdda: Long,
    val ultnumrefcadtit: Long,
    val ultnumseqcadtit: Long,
    val codifcedente: Long,
    val tipopescedenteori: String?,
    val cnpj_cpf_cedenteori: String,
    val nomecedenteori: String,
    val nomefantasiacedenteori: String?,
    val tipopescedente: String?,
    val cnpj_cpf_cedente: String?,
    val nomecedente: String?,
    val nomefantasiacedente: String?,
    val tipopessacado: String,
    val cnpj_cpf_sacado: String,
    val nomesacado: String,
    val nomefantasiasacado: String,
    val tipopessacador: String,
    val cnpj_cpf_sacador: String,
    val nomesacador: String,
    val identnossonumero: String,
    val numcodbarras: String,
    val linhadigitavel: String,
    val datavencimento: String,
    val vlrtitulo: Double,
    val codespeciedoc: String,
    val dataemissao: String,
    val qtdediasprotesto: Int?,
    val datalimpagto: String,
    val tipopagto: Int,
    val numparcela: Int?,
    val qtdparcela: Int?,
    val indtitulonegociado: String,
    val indbloqueio: String,
    val indparcial: String,
    val vlrabatimento: Double,
    val datamora: String?,
    val codmora: String = COD_MORA_ISENTO,
    val vlrpercmora: Double,
    val datamulta: String?,
    val codmulta: String = COD_MULTA_ISENTO,
    val vlrpercmulta: Double,
    val datadesconto01: String?,
    val coddesconto01: String,
    val vlrpercdesconto01: Double,
    val datadesconto02: String?,
    val coddesconto02: String?,
    val vlrpercdesconto02: Double,
    val datadesconto03: String?,
    val coddesconto03: String?,
    val vlrpercdesconto03: Double,
    val indvalorperc_min: String,
    val vlrmintitulo: Double,
    val indvalorperc_max: String,
    val vlrmaxtitulo: Double,
    val tipocalculo: String,
    val tipoautrecdivergente: String,
    val aceite: String?,
    val sitpagamento: String?,
    val sitpagamentocip: String,
    val dthrsittitulo: String?,
    val datahoradda: String?,
    val baixasOperacionais: List<BaixaTO>,
    val baixasEfetivas: List<BaixaTO>
)

data class BaixaTO(
    @JsonAlias("agebcobaixa_e")
    val agebcobaixa: String?,
    @JsonAlias("codbcobaixa_e")
    val codbcobaixa: String?,
    @JsonAlias("canal_e")
    val canal: String?,
    @JsonAlias("cnpj_cpf_port_e")
    val cnpj_cpf_port: String?,
    @JsonAlias("nome_port_e")
    val nome_port: String?,
    @JsonAlias("dataprocbaixa_e")
    val dataprocbaixa: String?,
    @JsonAlias("meio_e")
    val meio: String?,
    @JsonAlias("numidentbaixa_e")
    val numidentbaixa: Long,
    @JsonAlias("seqbaixa_e")
    val seqbaixa: Int,
    @JsonAlias("tpbaixa_e")
    val tpbaixa: Int,
    @JsonAlias("vlrbaixa_e")
    val vlrbaixa: Double
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class NullableDDABillTO(
    val numidentcdda: Long?,
    val ultnumrefcadtit: Long?,
    val ultnumseqcadtit: Long?,
    val codifcedente: Long?,
    val tipopescedenteori: String?,
    val cnpj_cpf_cedenteori: String?,
    val nomecedenteori: String?,
    val nomefantasiacedenteori: String?,
    val tipopescedente: String?,
    val cnpj_cpf_cedente: String?,
    val nomecedente: String?,
    val nomefantasiacedente: String?,
    val tipopessacado: String?,
    val cnpj_cpf_sacado: String?,
    val nomesacado: String?,
    val nomefantasiasacado: String?,
    val tipopessacador: String?,
    val cnpj_cpf_sacador: String?,
    val nomesacador: String?,
    val identnossonumero: String?,
    val numcodbarras: String?,
    val linhadigitavel: String?,
    val datavencimento: String?,
    val vlrtitulo: Double?,
    val codespeciedoc: String?,
    val dataemissao: String?,
    val qtdediasprotesto: Int?,
    val datalimpagto: String?,
    val tipopagto: Int?,
    val numparcela: Int?,
    val qtdparcela: Int?,
    val indtitulonegociado: String?,
    val indbloqueio: String?,
    val indparcial: String?,
    val vlrabatimento: Double?,
    val datamora: String?,
    val codmora: String? = COD_MORA_ISENTO,
    val vlrpercmora: Double?,
    val datamulta: String?,
    val codmulta: String? = COD_MULTA_ISENTO,
    val vlrpercmulta: Double?,
    val datadesconto01: String?,
    val coddesconto01: String?,
    val vlrpercdesconto01: Double?,
    val datadesconto02: String?,
    val coddesconto02: String?,
    val vlrpercdesconto02: Double?,
    val datadesconto03: String?,
    val coddesconto03: String?,
    val vlrpercdesconto03: Double?,
    val indvalorperc_min: String?,
    val vlrmintitulo: Double?,
    val indvalorperc_max: String?,
    val vlrmaxtitulo: Double?,
    val tipocalculo: String?,
    val tipoautrecdivergente: String?,
    val aceite: String?,
    val sitpagamento: String?,
    val sitpagamentocip: String?,
    val dthrsittitulo: String?,
    val datahoradda: String?,
    val baixasOperacionais: List<NullableBaixaTO>?,
    val baixasEfetivas: List<NullableBaixaTO>?
)

data class NullableBaixaTO(
    @JsonAlias("agebcobaixa_e")
    val agebcobaixa: String?,
    @JsonAlias("codbcobaixa_e")
    val codbcobaixa: String?,
    @JsonAlias("canal_e")
    val canal: String?,
    @JsonAlias("cnpj_cpf_port_e")
    val cnpj_cpf_port: String?,
    @JsonAlias("nome_port_e")
    val nome_port: String?,
    @JsonAlias("dataprocbaixa_e")
    val dataprocbaixa: String?,
    @JsonAlias("meio_e")
    val meio: String?,
    @JsonAlias("numidentbaixa_e")
    val numidentbaixa: Long?,
    @JsonAlias("seqbaixa_e")
    val seqbaixa: Int?,
    @JsonAlias("tpbaixa_e")
    val tpbaixa: Int?,
    @JsonAlias("vlrbaixa_e")
    val vlrbaixa: Double?
)