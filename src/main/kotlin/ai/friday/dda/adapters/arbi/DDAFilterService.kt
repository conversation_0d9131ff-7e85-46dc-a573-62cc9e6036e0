package ai.friday.dda.adapters.arbi

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.andAppend
import ai.friday.dda.app.tenant.TenantName
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.lettuce.core.api.StatefulRedisConnection
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.math.BigInteger
import java.security.MessageDigest
import java.time.Duration
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface DDAFilterService {
    fun filter(dda: DDABillTO, tenantName: TenantName): Boolean
}

@Singleton
@Requires(notEnv = [PIC_PAY_ENV])
class DDADuplicateFilterService(
    private val redis: StatefulRedisConnection<String, String?>
) : DDAFilterService {
    private val logger = LoggerFactory.getLogger(DDADuplicateFilterService::class.java)

    private val mapper = ObjectMapper()
        .registerModule(
            KotlinModule.Builder()
                .configure(KotlinFeature.NullIsSameAsDefault, enabled = true)
                .build()
        )

    private val dateTimeFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

    override fun filter(dda: DDABillTO, tenantName: TenantName): Boolean {
        val serializedDDA = mapper.writeValueAsString(dda)

        val key = "DDA_BILL#${tenantName.value}#${md5(serializedDDA)}"

        val markers = Markers.append("key", key)

        val command = redis.sync()

        val previous = command.getset(key, serializedDDA)
        command.expire(key, Duration.ofDays(1))

        if (previous == null) {
            markers.andAppend("added", "true")
                .andAppend("value", dda)
        } else {
            markers.andAppend("added", "false")

            if (previous != serializedDDA) {
                markers.andAppend("previous", previous)
                    .andAppend("serializedDDA", serializedDDA)
            }
        }

        logger.trace(markers, "DDADuplicateFilterService#filter")

        return previous != serializedDDA
    }

    private fun md5(value: String): String {
        val md = MessageDigest.getInstance("MD5")
        return BigInteger(1, md.digest(value.toByteArray())).toString(16).padStart(32, '0')
    }
}

@Singleton
@Requires(env = [PIC_PAY_ENV])
class NoOpDDAFilterService : DDAFilterService {
    override fun filter(dda: DDABillTO, tenantName: TenantName): Boolean {
        return true
    }
}