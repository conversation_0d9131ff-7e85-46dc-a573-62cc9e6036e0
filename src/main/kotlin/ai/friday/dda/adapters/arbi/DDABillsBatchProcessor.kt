package ai.friday.dda.adapters.arbi

import ai.friday.dda.app.interfaces.FileProcessResponseTO
import ai.friday.dda.app.tenant.TenantName
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import jakarta.inject.Singleton
import java.io.InputStream
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val COD_MORA_ISENTO = "5"
const val COD_MULTA_ISENTO = "3"

@Singleton
class DDABillsBatchProcessor(
    private val ddaFilterService: DDAFilterService,
    private val sendMessageProcessor: SendMessageProcessor
) {
    private val mapper: ObjectMapper = ObjectMapper()
        .registerModule(
            KotlinModule.Builder()
                .configure(KotlinFeature.NullIsSameAsDefault, enabled = true)
                .build()
        )

    fun process(inputStream: InputStream, tenantName: TenantName): FileProcessResponseTO {
        inputStream.use {
            val values: List<DDABillTO> = mapper.readValue<List<DDABillTO>>(
                it,
                mapper.typeFactory.constructCollectionType(List::class.java, DDABillTO::class.java)
            ).map { ddaBill ->
                // O Arbi duplica as baixas efetivas para cada baixa operacional
                ddaBill.copy(baixasEfetivas = ddaBill.baixasEfetivas.toSet().toList())
            }

            val filteredValues = values.filter { dda -> ddaFilterService.filter(dda, tenantName) }

            if (filteredValues.isNotEmpty()) {
                sendMessageProcessor.process(filteredValues, tenantName)
            }

            return FileProcessResponseTO(
                totalItems = values.size,
                processedItems = filteredValues.size
            )
        }
    }

    fun processFileWithError(inputStream: InputStream, tenantName: TenantName): Result<Unit> {
        return runCatching {
            inputStream.use {
                val values: List<NullableDDABillTO> = mapper.readValue(
                    it,
                    mapper.typeFactory.constructCollectionType(List::class.java, NullableDDABillTO::class.java)
                )
                for (value in values) {
                    val valueAsString = mapper.writeValueAsString(value)

                    try {
                        mapper.readValue(valueAsString, DDABillTO::class.java)
                        sendMessageProcessor.processWithError(valueAsString, tenantName)
                    } catch (e: Exception) {
                        logger.error(append("value", value), "DDABillsBatchProcessor", e)
                    }
                }
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DDABillsBatchProcessor::class.java)
    }
}