package ai.friday.dda.adapters.arbi

import ai.friday.dda.Err
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import io.micronaut.context.annotation.ConfigurationProperties
import java.util.UUID

private const val ARBI_BANK_NO = "213"
private const val ARBI_ROUTING_NO = "00019"

@ConfigurationProperties("integrations.arbi")
class ArbiConfiguration {
    lateinit var host: String
    lateinit var grantCodePath: String
    lateinit var accessTokenPath: String
    lateinit var validatePath: String
    lateinit var validateV2Path: String
    lateinit var calculatePath: String
    lateinit var checkingPath: String
    lateinit var getStatementPath: String
    lateinit var tedStatusPath: String
    lateinit var checkingV2Path: String
    lateinit var getStatementV2Path: String
    lateinit var tedStatusV2Path: String
    lateinit var ddaPath: String
    lateinit var ddaV2Path: String
    lateinit var ddaCadastroPath: String
    lateinit var ddaCadastroLotePath: String
    lateinit var cadastroPFPath: String
    lateinit var domainPath: String
    lateinit var domainV2Path: String
    lateinit var encerrarContaPath: String
    lateinit var cadastroEnderecoPath: String
    lateinit var userToken: String
    lateinit var clientId: String
    lateinit var clientSecret: String
    lateinit var contaTitular: String
    lateinit var contaCashin: String
    lateinit var inscricao: String
    lateinit var tipoPessoa: String
    lateinit var paymentTimeLimit: Number
    var gatewayV2ContaCorrente: Boolean = false
}

data class DDATO(val dda: DDA)

data class DDA(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: IdRequisicao = IdRequisicao(),
    @JsonProperty("idmodulo") val idModulo: String = "2",
    @JsonProperty("idtransacao") val idTransacao: String = "8",
    @JsonProperty("bancotitular") val bancoTitular: String = ARBI_BANK_NO,
    @JsonProperty("agenciatitular") val agenciaTitular: String = ARBI_ROUTING_NO,
    @JsonProperty("contatitular") val contaTitular: String,
    @JsonProperty("inscricao") val inscricao: String,
    @JsonProperty("tipopessoa") val tipoPessoa: String,
    @JsonProperty("tipomanutencao") val tipoManutencao: String = "",
    @JsonProperty("grupoagregado") val grupoAgregado: String = "",
    @JsonProperty("tiporetornotitulos") val tipoRetornoTitulos: String,
    @JsonProperty("datavctotitulo") val dataVctoTitulo: String
)

data class DDAAgregado(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: IdRequisicao = IdRequisicao(),
    @JsonProperty("idmodulo") val idModulo: String = "2",
    @JsonProperty("idtransacao") val idTransacao: String = "9",
    @JsonProperty("inscricaotitular") val inscricaotitular: String,
    @JsonProperty("inscricaoagregado") val inscricaoagregado: String,
    @JsonProperty("tipopessoaagregado") val tipopessoaagregado: String = "F",
    @JsonProperty("tipomanutencao") val tipomanutencao: String // Nesta transação é possível: "A"- Alterar, "C"- Cancelar , "E"- Excluir e "I"- Incluir.
)

data class ArbiResponseTO(
    @JsonProperty("idtransacao") val idTransacao: Int,
    @JsonProperty("resultado") val resultado: String? = null,
    @JsonProperty("idstatus") val idStatus: Int,
    @JsonProperty("idrequisicaoparceiro") val idRequisicaoParceiro: String,
    @JsonProperty("idmodulo") val idModulo: Int,
    @JsonProperty("idrequisicaoarbi") val idRequisicaoArbi: String,
    @JsonProperty("descricaostatus") val descricaoStatus: String
)

data class IdRequisicao @JsonCreator constructor(@JsonValue val value: String = UUID.randomUUID().toString()) {
    init {
        if (value.length > 50) {
            throw IllegalArgumentException()
        }
    }
}

data class DomainTO(
    @JsonProperty("iddominio") val iddominio: Int,
    @JsonProperty("descricaodominio") val descricaodominio: String?,
    @JsonProperty("codigodominio") val codigodominio: String
)

object ArbiLoginErr : Err("ArbiLoginError")
object ArbiProviderErr : Err("ArbiProviderError")