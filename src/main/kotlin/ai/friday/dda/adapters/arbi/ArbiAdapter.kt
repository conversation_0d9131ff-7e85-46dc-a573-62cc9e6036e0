package ai.friday.dda.adapters.arbi

import ai.friday.dda.andAppend
import ai.friday.dda.app.DdaItem
import ai.friday.dda.app.DdaProviderException
import ai.friday.dda.app.DdaProviderLoginException
import ai.friday.dda.app.Document
import ai.friday.dda.app.banking.FinancialInstitution
import ai.friday.dda.app.bill.BarCode
import ai.friday.dda.app.interfaces.DDAProviderService
import ai.friday.dda.app.interfaces.FinancialInstitutionError
import ai.friday.dda.app.isCPF
import arrow.core.Either
import arrow.core.getOrHandle
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Primary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private val arbiDateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
val vctoTituloDateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

@Primary
@Singleton
open class ArbiAdapter(
    @param:Client(
        value = "\${integrations.arbi.host}"
    ) private val httpClient: HttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: ArbiAuthenticationManager
) : DDAProviderService {

    override fun getBills(dueDate: LocalDate, document: Document?): List<DdaItem> {
        return getBills(
            DDATO(
                dda = DDA(
                    inscricaoParceiro = configuration.inscricao,
                    tokenUsuario = configuration.userToken,
                    contaTitular = configuration.contaTitular,
                    inscricao = document?.value ?: configuration.inscricao,
                    tipoPessoa = if (isCPF(document?.value ?: configuration.inscricao)) "F" else "J",
                    tipoRetornoTitulos = "T",
                    dataVctoTitulo = dueDate.format(vctoTituloDateFormat)
                )
            )
        )
    }

    private fun getBills(dda: DDATO): List<DdaItem> {
        val token = authenticationManager.getToken()
        val httpRequest = HttpRequest.POST(configuration.ddaPath, dda)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        try {
            val response = httpClient.toBlocking().retrieve(
                httpRequest,
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING
            )
            logger.info(append("response", response), "ArbiGetDDABills")
            @Suppress("UNCHECKED_CAST")
            val mappedResponse: List<Map<String, String>> = response.map {
                jacksonObjectMapper().readValue(
                    it.resultado,
                    Map::class.java
                )
            } as List<Map<String, String>>
            return mappedResponse.map { convertToDDABill(it) }
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NO_CONTENT) {
                logger.info(append("response", "EMPTY_BODY"), "ArbiGetDDABills", e)
                return emptyList()
            }

            checkUnauthorized(e, "ArbiGetDDABills")
            logger.error(
                append("request", dda).andAppend("response", e.response.getBody(String::class.java).get()),
                "ArbiGetDDABills",
                e
            )
            throw DdaProviderException()
        }
    }

    override fun add(documents: List<Document>) = TODO()

    override fun remove(document: Document) = TODO()

    fun listFinancialInstitutions(): Either<FinancialInstitutionError, List<FinancialInstitution>> {
        val response = fetchDomain(41).getOrHandle { return it.left() }

        logger.info(append("response", response), "ArbiAdapter#listFinancialInstitutions")
        return response.map {
            FinancialInstitution(
                compe = it.codigodominio.takeLast(3).toLongOrNull(),
                name = it.descricaodominio.orEmpty(),
                ispb = it.codigodominio.take(8)
            )
        }.right()
    }

    private fun checkUnauthorized(e: HttpClientResponseException, context: String) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(append("message", "Token is expired"), context)
            authenticationManager.cleanTokens()
            throw DdaProviderLoginException()
        }
    }

    private fun convertToDDABill(map: Map<String, String>): DdaItem {
        return DdaItem(
            amount = map["valor"]?.toDouble() ?: 0.0,
            barcode = BarCode(number = map["codbarras"] as String, digitable = map["linhadigitavel"] as String),
            document = Document(map.getOrElse("inscricaosacado") { throw IllegalStateException() }),
            dueDate = LocalDate.parse(map.getOrElse("datavctotitulo") { throw IllegalStateException() }, arbiDateFormat)
        )
    }

    private fun fetchDomain(domainNumber: Int): Either<FinancialInstitutionError, List<DomainTO>> {
        val token = authenticationManager.getToken()
        val httpRequest = HttpRequest.POST(configuration.domainPath, """{"dominios":{"iddominio":$domainNumber}}""")
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        return try {
            httpClient.toBlocking().retrieve(
                httpRequest,
                Argument.listOf(DomainTO::class.java),
                Argument.STRING
            ).right()
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e, "FetchDomain")
            logger.error(append("response", e.response.getBody(String::class.java).get()), "FetchDomain", e)
            FinancialInstitutionError.UnknownError.left()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiAdapter::class.java)
    }
}