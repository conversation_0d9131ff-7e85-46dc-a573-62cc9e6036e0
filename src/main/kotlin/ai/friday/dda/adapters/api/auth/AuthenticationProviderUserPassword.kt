package ai.friday.dda.adapters.api.auth

import ai.friday.dda.app.SecurityRole
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.security.authentication.AuthenticationProvider
import io.micronaut.security.authentication.AuthenticationRequest
import io.micronaut.security.authentication.AuthenticationResponse
import jakarta.inject.Singleton
import org.reactivestreams.Publisher
import reactor.core.publisher.Mono

@Singleton
class AuthenticationProviderUserPassword(
    @Property(name = "internal-auth.identity") private val internalIdentity: String,
    @Property(name = "internal-auth.secret") private val internalSecret: String,
) : AuthenticationProvider {

    override fun authenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Publisher<AuthenticationResponse> {

        return Mono.create { emitter ->
            if (authenticationRequest.identity == internalIdentity && authenticationRequest.secret == internalSecret) {
                emitter.success(
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(SecurityRole.INTERNAL.name),
                    )
                )
            } else {
                emitter.error(AuthenticationResponse.exception())
            }
        }
    }
}