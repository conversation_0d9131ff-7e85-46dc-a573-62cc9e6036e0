package ai.friday.dda.adapters.api

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.jobs.PicPayDDADeregisterJob
import ai.friday.dda.app.SecurityRole
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import org.slf4j.LoggerFactory

@Secured(SecurityRole.Code.INTERNAL)
@Controller("/job")
@Requires(env = [PIC_PAY_ENV])
class JobsController(private val picPayDDADeregisterJob: PicPayDDADeregisterJob) {

    @Post("/opt-out")
    fun optOut(): HttpResponse<*> {
        LOG.info("JobsController#optOut")
        picPayDDADeregisterJob.execute()
        return HttpResponse.noContent<Unit>()
    }

    @Post("/opt-out/enhanced{?rows}")
    fun enhancedOptOut(@QueryValue(defaultValue = "1500") rows: Int): HttpResponse<*> {
        LOG.info("JobsController#optOut")
        picPayDDADeregisterJob.enhancedOptOut(rows)
        return HttpResponse.noContent<Unit>()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(JobsController::class.java)
    }
}