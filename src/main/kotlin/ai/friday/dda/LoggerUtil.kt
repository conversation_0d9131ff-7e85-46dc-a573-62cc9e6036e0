package ai.friday.dda

import io.micronaut.core.type.Argument
import io.micronaut.http.client.exceptions.HttpClientResponseException
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers

fun log(vararg args: Pair<String, Any?>) = Markers.empty().and(*args)

fun LogstashMarker.and(vararg args: Pair<String, Any?>): LogstashMarker =
    args.toMap().filterKeys { it.lowercase() != "message" }.filterValues { it != null }
        .takeIf { it.isNotEmpty() }
        ?.map {
            // FIXME - tentear resolver isso diretamente na configuracao do Logstash
            val value = when (it.value) {
                is LocalDate, is LocalDateTime, is ZonedDateTime -> it.value.toString()
                else -> it.value
            }
            this.and<LogstashMarker>(Markers.append(it.key, value))
        }
        ?.reduce { first, second -> first.and(second) } ?: this

fun HttpClientResponseException.markers(): LogstashMarker = log(
    "status" to response.status,
    "statusCode" to response.status.code,
    "responseBody" to responseBodyAsStringOrNull(),
    "errorMessage" to message,
)

fun HttpClientResponseException.responseBodyAsStringOrNull(): String? =
    response.getBody(Argument.STRING).orElse(null)