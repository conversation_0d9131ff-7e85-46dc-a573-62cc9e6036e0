package ai.friday.dda.app.concessionariadireto

import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItem
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItemStatus
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoProviderService
import ai.friday.dda.andAppend
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.QueueMessageBatch
import ai.friday.dda.app.dateFormatWithBrazilTimeZone
import ai.friday.dda.app.interfaces.ConcessionariaDiretoRepository
import ai.friday.dda.app.interfaces.FileProcessResponseTO
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.app.interfaces.activeConcessionariaBillsBean
import ai.friday.dda.app.interfaces.inactiveConcessionariaBillsBean
import ai.friday.dda.mapper
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.io.InputStream
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class MulticomService(
    private val concessionariaDiretoProviderService: ConcessionariaDiretoProviderService,
    private val messagePublisher: MessagePublisher,
    private val concessionariaDiretoRepository: ConcessionariaDiretoRepository,
    @Named(activeConcessionariaBillsBean) private val activeBillsPublisherConfiguration: MessagePublisherConfiguration,
    @Named(inactiveConcessionariaBillsBean) private val inactiveBillsPublisherConfiguration: MessagePublisherConfiguration
) {
    fun processFile(inputStream: InputStream): FileProcessResponseTO {
        val startTime = BrazilZonedDateTimeSupplier.getZonedDateTime()
        val validItems = concessionariaDiretoProviderService.processFile(inputStream)
        val markers = Markers.append("validItems", validItems.size)

        val createdItems = validItems.filter { newItem ->
            val existingItem = concessionariaDiretoRepository.find(newItem.barCode, newItem.dueDate)

            if (existingItem == null) {
                concessionariaDiretoRepository.save(newItem)
                true
            } else {
                concessionariaDiretoRepository.save(
                    existingItem.copy(
                        lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime(),
                        inactivityCounter = 0
                    )
                )
                false
            }
        }
        markers.andAppend("newItems", createdItems.size)

        if (createdItems.isNotEmpty()) {
            messagePublisher.sendMessageBatch(
                QueueMessageBatch(
                    queueName = activeBillsPublisherConfiguration.queueName,
                    messages = createdItems.map { item ->
                        mapper.writeValueAsString(item.toConcessionariaDiretoTO())
                    }
                )
            )
        }

        if (validItems.isNotEmpty()) {
            updateMissingItems(startTime)
        }

        logger.info(markers, "MulticomService#processFile")

        return FileProcessResponseTO(
            totalItems = validItems.size,
            processedItems = createdItems.size
        )
    }

    private fun updateMissingItems(lastSeen: ZonedDateTime) {
        concessionariaDiretoRepository.findLastSeenBillsSince(lastSeen = lastSeen).forEach {
            concessionariaDiretoRepository.save(it.copy(inactivityCounter = it.inactivityCounter + 1))
        }
    }

    fun checkExpiredBills() {
        val markers = Markers.empty()
        try {
            val entries = concessionariaDiretoRepository.findLastSeenBillsSince(
                BrazilZonedDateTimeSupplier.getZonedDateTime().minusDays(1)
            ).filter { it.inactivityCounter >= 2 }

            markers.andAppend("expiredBillSize", entries.size)
            if (entries.isNotEmpty()) {
                messagePublisher.sendMessageBatch(
                    QueueMessageBatch(
                        queueName = inactiveBillsPublisherConfiguration.queueName,
                        messages = entries.map { item ->
                            mapper.writeValueAsString(item.toConcessionariaDiretoTO())
                        }
                    )
                )
                entries.forEach {
                    concessionariaDiretoRepository.save(it.copy(status = ConcessionariaDiretoItemStatus.INACTIVE))
                }
            }
            logger.info(markers, "MulticomService#checkExpiredBills")
        } catch (e: Exception) {
            logger.error(markers, "MulticomService#checkExpiredBills", e)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MulticomService::class.java)
    }
}

private fun ConcessionariaDiretoItem.toConcessionariaDiretoTO() = ConcessionariaDiretoTO(
    amount = this.amount,
    barcode = this.barCode.number,
    document = this.document.value,
    dueDate = this.dueDate.format(dateFormatWithBrazilTimeZone),
    description = this.description,
    provider = this.provider.name,
    providerBillId = this.providerBillId
)

internal data class ConcessionariaDiretoTO(
    val amount: Long,
    val barcode: String,
    val document: String,
    val dueDate: String,
    val description: String,
    val provider: String,
    val providerBillId: String
)