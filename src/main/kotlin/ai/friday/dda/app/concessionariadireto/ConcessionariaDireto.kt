package ai.friday.billpayment.app.concessionariadireto

import ai.friday.dda.app.Document
import ai.friday.dda.app.bill.BarCode
import java.io.InputStream
import java.time.LocalDate
import java.time.ZonedDateTime

interface ConcessionariaDiretoProviderService {
    fun processFile(inputStream: InputStream): List<ConcessionariaDiretoItem>
}

data class ConcessionariaDiretoItem(
    val amount: Long,
    val barCode: BarCode,
    val document: Document,
    val dueDate: LocalDate,
    val description: String,
    val provider: ConcessionariaDiretoProvider,
    val providerBillId: String,
    val status: ConcessionariaDiretoItemStatus,
    val inactivityCounter: Long,
    val lastSeen: ZonedDateTime
)

enum class ConcessionariaDiretoProvider {
    MULTICOM
}

enum class ConcessionariaDiretoItemStatus {
    ACTIVE, INACTIVE
}

enum class ConcessionariaDiretoItemSaveResult {
    CREATED, UPDATED
}