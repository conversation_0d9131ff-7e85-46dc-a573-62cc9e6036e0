// ktlint-disable filename - pulo do gato do linter
package ai.friday.dda.app.metrics

import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.log
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.Meter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import io.micrometer.core.instrument.config.NamingConvention
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.Duration
import java.util.concurrent.TimeUnit
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Context
@Requires(property = "micronaut.metrics.enabled", value = "true")
@Singleton
private class Metrifier(
    @Property(name = "micronaut.application.name") prefix: String,
    @Property(name = "management.metrics.tags") commonTags: Map<String, Any>,
    registry: MeterRegistry,
) {
    init {
        Companion.prefix = prefix
        val common = buildList(commonTags.size * 2) {
            commonTags.forEach { (key, value) ->
                this.add(key); this.add(value.toString())
            }
        }

        registry.config()
            .namingConvention(NamingConvention.dot)
            .commonTags(*common.toTypedArray())

        Companion.registry = registry
    }

    companion object {
        private val logger = LoggerFactory.getLogger(Metrifier::class.java)

        private lateinit var prefix: String
        private lateinit var registry: MeterRegistry

        fun isInitialized() = this::prefix.isInitialized && this::registry.isInitialized

        private fun validate(vararg tags: Pair<String, Any?>) =
            tags.groupingBy { it.first }.eachCount().count { it.value > 1 } == 0

        private fun getOrCreateMeter(name: String, type: MetricType, vararg possibleTags: Pair<String, Any?>): Meter? {
            if (!validate(*possibleTags)) return null

            val tags = possibleTags.map { if (it.second == null) it.first to "undefined" else it.first to it.second }
            val tagsArray = ArrayList<String>(tags.size * 2)
            tags.forEach { tagsArray.addAll(arrayOf(it.first, it.second.toString().lowercase())) }

            return when (type) {
                MetricType.COUNTER -> registry.counter(name, *tagsArray.toTypedArray())
                MetricType.TIMED -> registry.timer(name, *tagsArray.toTypedArray())
            }
        }

        private fun count(name: String, vararg tags: Pair<String, Any?>) {
            val markers = log("metric_name" to name, "tags" to tags)
            try {
                val metric = getOrCreateMeter("$prefix.$name.total", MetricType.COUNTER, *tags) as? Counter
                if (metric == null) {
                    logger.warn(markers, "inc#duplicate_tags")
                    return
                }
                metric.increment()
            } catch (ex: Exception) {
                logger.error(markers, "inc#error", ex)
            }
        }

        private fun time(
            name: String,
            baseUnit: TimeUnit? = TimeUnit.MILLISECONDS,
            elapsed: Long,
            vararg tags: Pair<String, Any?>,
        ) {
            val updatedTags = tags.toMutableList()
            updatedTags.add("unit" to baseUnit!!.name)

            val markers = log("metric_name" to name, "elapsed" to elapsed, "tags" to updatedTags)
            try {
                val metric =
                    getOrCreateMeter("$prefix.$name.duration", MetricType.TIMED, *updatedTags.toTypedArray()) as? Timer
                if (metric == null) {
                    logger.warn(markers, "time#duplicate_tags")
                    return
                }

                metric.record(elapsed, baseUnit)
            } catch (ex: Exception) {
                logger.error(markers, "time#error", ex)
            }
        }

        fun push(metric: Metrics, vararg tags: Pair<String, Any?>) = when (metric) {
            is AbstractFridayCountMetrics -> count(metric.name(), *tags)
            is AbstractTimeByElapsedMetrics -> time(metric.name(), TimeUnit.MILLISECONDS, metric.elapsed(), *tags)
            is AbstractTimeByDateMetrics -> {
                val elapsed = Duration.between(metric.time(), BrazilZonedDateTimeSupplier.getZonedDateTime())
                val value = when (metric.baseUnit()) {
                    TimeUnit.DAYS -> elapsed::toDays
                    TimeUnit.HOURS -> elapsed::toHours
                    TimeUnit.MINUTES -> elapsed::toMinutes
                    TimeUnit.SECONDS -> elapsed::toSeconds
                    else -> elapsed::toMillis
                }

                time(metric.name(), metric.baseUnit(), value.invoke(), *tags)
            }

            else -> logger.warn(Markers.append("metric_name", metric.name()), "push#not_implemented")
        }
    }
}

fun metricRegister(metric: Metrics, vararg possibleTags: Pair<String, Any?>) =
    when {
        !Metrifier.isInitialized() -> Unit
        else -> Metrifier.push(metric, *possibleTags)
    }