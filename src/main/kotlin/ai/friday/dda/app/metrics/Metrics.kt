package ai.friday.dda.app.metrics

import java.time.ZonedDateTime
import java.util.concurrent.TimeUnit

interface Metrics {
    fun name(): String
}

enum class MetricType {
    COUNTER, TIMED
}

abstract class AbstractMetrics : Metrics {
    override fun name(): String {
        val clazzName = this::class.java.simpleName
        return clazzName.fold(StringBuilder(clazzName.length)) { acc, c ->
            if (c in 'A'..'Z') (if (acc.isNotEmpty()) acc.append('.') else acc).append(c + ('a' - 'A'))
            else acc.append(c)
        }.toString()
    }
}

abstract class AbstractTimeByElapsedMetrics : AbstractMetrics() {
    abstract fun elapsed(): Long
}

abstract class AbstractTimeByDateMetrics : AbstractMetrics() {
    abstract fun time(): ZonedDateTime
    abstract fun baseUnit(): TimeUnit?
}

abstract class AbstractFridayCountMetrics(open val value: Number = 1) : AbstractMetrics()