package ai.friday.dda.app.job

import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.log
import io.micronaut.context.annotation.Property
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.Duration
import java.time.Instant
import org.slf4j.LoggerFactory

@Singleton
open class JobManager(val jobs: List<AbstractJob>) : ApplicationEventListener<Any> {
    private val logger = LoggerFactory.getLogger(JobManager::class.java)

    @field:Property(name = "shedlock.defaults.lock-at-least-for", defaultValue = "PT0S")
    private lateinit var defaultLockAtLeastFor: String

    @field:Property(name = "shedlock.defaults.lock-at-most-for")
    private lateinit var defaultLockAtMostFor: String

    private val shoutDownPoolingInterval = 10000L

    @NewSpan
    override fun onApplicationEvent(event: Any) {
        when (event) {
            is ServiceReadyEvent -> onServiceReady()
            is ApplicationShutdownEvent -> onApplicationShutdown()
        }
    }

    private fun onServiceReady() {
        val markers = log(
            "jobs" to jobs.size,
            "defaultLockAtLeastFor" to defaultLockAtLeastFor,
            "defaultLockAtMostFor" to defaultLockAtMostFor
        )

        logger.info(markers, "JobManager#onServiceReady")

        jobs.forEach {
            it.initialize(
                defaultLockAtLeastFor = defaultLockAtLeastFor,
                defaultLockAtMostFor = defaultLockAtMostFor,
            )
        }
    }

    private fun onApplicationShutdown() {
        logger.info("JobManager#onApplicationShutdown")

        val start = BrazilZonedDateTimeSupplier.getZonedDateTime().toInstant()

        jobs.forEach { it.beginShutdown() }

        var waitTime = calcShutdownElapsedTime(start)

        jobs.forEach {
            while (it.running && it.shutdownGracefully && it.shutdownGracefullyMaxWaitTime > waitTime) {
                logger.warn(log("jobName" to it.jobName), "JobManager#onApplicationShutdown")
                Thread.sleep(shoutDownPoolingInterval)

                waitTime = calcShutdownElapsedTime(start)
            }
        }

        jobs.forEach {
            val markers = log("jobName" to it.jobName)
            if (it.running && it.shutdownGracefully && waitTime >= it.shutdownGracefullyMaxWaitTime) {
                logger.error(markers, "JobManager#onApplicationShutdown")
            } else {
                logger.info(markers, "JobManager#onApplicationShutdown")
            }
            it.unlock()
        }
    }

    private fun calcShutdownElapsedTime(start: Instant): Long {
        val now = BrazilZonedDateTimeSupplier.getZonedDateTime().toInstant()
        return Duration.between(start, now).toMinutes()
    }
}