package ai.friday.dda.app.dda

import ai.friday.dda.Err
import ai.friday.dda.app.interfaces.DDARepository
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.app.interfaces.ddaBillsImportBean
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton

@Singleton
class DDARegisterService(
    @Named(ddaBillsImportBean) private val config: MessagePublisherConfiguration,
    private val repository: DDARepository,
    private val publisher: MessagePublisher
) {
    fun handleOptInApproved(document: String): Either<Err, DDARegister> {
        val register = repository.findByDocument(document) ?: return DDARegisterResponseError.RegisterNotFound.left()

        val newDDAStatus = when (register.status) {
            DDAStatus.PENDING, DDAStatus.REQUESTING, DDAStatus.DENIED -> DDAStatus.REQUESTED
            DDAStatus.PENDING_MIGRATION_OPTIN, DDAStatus.MIGRATING_OPTIN -> DDAStatus.PENDING_MIGRATION_OPTOUT
            else -> return DDARegisterResponseError.InvalidStatus.left()
        }

        // send delayed message to start full import at dda-service
        publisher.sendMessage(config.queueName, mapOf("document" to document), config.delay)
            .mapLeft { return it.left() }

        return repository.save(register.copy(status = newDDAStatus, provider = DDAProvider.PICPAY)).right()
    }

    fun handleOptInReproved(document: String): Either<DDARegisterResponseError, DDARegister> {
        val register = repository.findByDocument(document) ?: return DDARegisterResponseError.RegisterNotFound.left()

        return when (DenyableStatuses.contains(register.status)) {
            true -> repository.save(register.copy(status = DDAStatus.DENIED)).right()
            false -> DDARegisterResponseError.InvalidStatus.left()
        }
    }

    fun handleOptOutApproved(document: String): Either<Err, DDARegister> {
        val register = repository.findByDocument(document) ?: return DDARegisterResponseError.RegisterNotFound.left()

        if (register.status !in OptOutValidStatuses) {
            return DDARegisterResponseError.InvalidStatus.left()
        }

        // fecha tb

        return repository.save(register.copy(status = DDAStatus.CLOSED)).right()
    }

    companion object {
        val OptinValidStatuses = listOf(DDAStatus.PENDING, DDAStatus.REQUESTING)
        val MigrationValidStatuses = listOf(DDAStatus.PENDING_MIGRATION_OPTIN, DDAStatus.MIGRATING_OPTIN)

        val ApprovableStatuses = MigrationValidStatuses + OptinValidStatuses + DDAStatus.DENIED

        val DenyableStatuses = ApprovableStatuses + listOf(DDAStatus.REQUESTED, DDAStatus.PENDING_MIGRATION_OPTOUT)

        val OptOutValidStatuses = listOf(DDAStatus.PENDING_CLOSE, DDAStatus.CLOSING)
    }
}

sealed class DDARegisterResponseError(m: String = "") : Err(m) {
    object RegisterNotFound : DDARegisterResponseError("register not found")
    object InvalidStatus : DDARegisterResponseError("register status invalid")
}