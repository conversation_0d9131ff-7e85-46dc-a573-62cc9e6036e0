package ai.friday.dda.app.dda

import ai.friday.dda.Err
import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.picpay.messaging.DDABillMessageTO
import ai.friday.dda.app.interfaces.DDAProviderServiceV2
import ai.friday.dda.app.interfaces.DDARequestFilter
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.app.interfaces.ddaBillsInternalBean
import arrow.core.Either
import arrow.core.getOrHandle
import arrow.core.left
import io.micronaut.context.annotation.Requires
import jakarta.inject.Named
import jakarta.inject.Singleton

@Singleton
@Requires(env = [PIC_PAY_ENV])
class DDAEventService(
    @Named(ddaBillsInternalBean) private val billsInternalPublisher: MessagePublisherConfiguration,
    private val provider: DDAProviderServiceV2,
    private val publisher: MessagePublisher
) {
    fun handleEvent(
        cipID: String,
        requestId: String,
        type: String
    ): Either<Err, Unit> {
        val filter = DDARequestFilter.CIPFilter(cipID)
        val ddaBill = provider.findByCipId(filter).getOrHandle { return it.left() }

        // send message to immediately consume and create bill at BillPayment
        return publisher.sendMessage(
            billsInternalPublisher.queueName,
            DDABillMessageTO.from(bill = ddaBill, requestId = requestId, operationType = type)
        )
    }
}