package ai.friday.dda.app.dda

import ai.friday.dda.Err
import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.PrintableSealedClass
import ai.friday.dda.adapters.picpay.messaging.DDABillMessageTO
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.app.interfaces.ddaActivationBean
import ai.friday.dda.app.interfaces.ddaBillsInternalBean
import arrow.core.Either
import arrow.core.getOrHandle
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.UUID
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@Singleton
@Requires(env = [PIC_PAY_ENV])
class DDAFullImportService(
    @Named(ddaBillsInternalBean) private val billsInternal: MessagePublisherConfiguration,
    @Named(ddaActivationBean) private val activation: MessagePublisherConfiguration,
    private val service: DDAService,
    private val publisher: MessagePublisher,
    private val configuration: FullImportConfiguration
) {
    fun handleFullImport(command: FullImportCommand): Either<Err, FullImportResult> {
        val ddas = service.findDDAByDocument(command.document).getOrHandle { return it.left() }
        // send message to immediately consume and create bill at BillPayment
        ddas.items.map {
            publisher.sendMessage(
                billsInternal.queueName,
                DDABillMessageTO.from(it, "full-import-${System.currentTimeMillis()}-${UUID.randomUUID()}", operationType = "IMPORT")
            )
        }

        if (ddas.isEmpty() && canRetry(command)) return FullImportResult.NoContent.right()

        // send delayed message of activation to BillPayment
        if (command.activateDDA) {
            val message = DDAActivationMessageTO(command.document, ddas.totalItems, DDAProvider.PICPAY)
            publisher.sendMessage(activation.queueName, message, activation.delay).getOrHandle { return it.left() }
        }

        return when (ddas.isEmpty()) {
            true -> FullImportResult.RetryPolicyExceeded.right()
            false -> FullImportResult.Success(ddas.totalItems).right()
        }
    }

    fun canRetry(command: FullImportCommand): Boolean {
        val delta = System.currentTimeMillis() - command.createdAtInMillis
        return command.attempts < configuration.maxAttempts && delta.toDuration(DurationUnit.MILLISECONDS).inWholeHours < configuration.maxHours
    }
}

sealed class FullImportResult : PrintableSealedClass() {
    object NoContent : FullImportResult()

    object RetryPolicyExceeded : FullImportResult()

    data class Success(val processedBills: Int) : FullImportResult()
}

data class FullImportCommand(val document: String, val createdAtInMillis: Long, val attempts: Long, val activateDDA: Boolean = true)

@ConfigurationProperties("fullImport.retryPolicy")
class FullImportConfiguration @ConfigurationInject constructor(val maxHours: Int, val maxAttempts: Int)