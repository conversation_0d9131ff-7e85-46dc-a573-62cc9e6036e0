package ai.friday.dda.app.dda

import ai.friday.dda.Err
import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.BillRegisterDataListResponse
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.DDAListErr
import ai.friday.dda.app.Document
import ai.friday.dda.app.interfaces.DDAProviderServiceV2
import ai.friday.dda.app.interfaces.DDARequestFilter
import ai.friday.dda.app.interfaces.Filters
import arrow.core.Either
import arrow.core.getOrHandle
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.LocalDate
import java.util.concurrent.atomic.AtomicInteger

@Singleton
@Requires(env = [PIC_PAY_ENV])
class DDAService(private val provider: DDAProviderServiceV2) {
    fun findDDAByDocument(document: String): Either<Err, BillRegisterDataListResponse> {
        val page = AtomicInteger(0)
        val items = mutableListOf<BillRegisterData>()

        do {
            val currentPage = page.incrementAndGet()

            val elements = provider.findByFilter(rangeFilter(document, currentPage))
                .getOrHandle { return DDAListErr(currentPage).left() }

            items.addAll(elements.items)
        } while (elements.totalItems >= currentPage * 10)

        return BillRegisterDataListResponse(totalItems = items.size, items = items).right()
    }

    private companion object {
        const val RANGE_DATE_PAST_DELTA_DAYS: Long = 30
        const val RANGE_DATE_FUTURE_DELTA_DAYS: Long = 365 * 10

        fun rangeFilter(document: String, page: Int?): DDARequestFilter.RangeFilter {
            val date: LocalDate = BrazilZonedDateTimeSupplier.getLocalDate()
            val pagination = Filters.Pagination(pageNum = page ?: 1)

            val range = Filters.DateRange(
                start = date.minusDays(RANGE_DATE_PAST_DELTA_DAYS),
                end = date.plusDays(RANGE_DATE_FUTURE_DELTA_DAYS)
            )

            return DDARequestFilter.RangeFilter(range, Document(document), pagination)
        }
    }
}