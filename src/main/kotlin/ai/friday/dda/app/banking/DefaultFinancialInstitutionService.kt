package ai.friday.dda.app.banking

import ai.friday.dda.FileUtils
import ai.friday.dda.app.interfaces.FinancialIdentifier
import ai.friday.dda.app.interfaces.FinancialInstitutionError
import ai.friday.dda.app.interfaces.FinancialInstitutionProvider
import ai.friday.dda.app.interfaces.FinancialInstitutionService
import arrow.core.Either
import arrow.core.getOrHandle
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.scheduling.annotation.Scheduled
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton

@Singleton
open class DefaultFinancialInstitutionService(private val provider: FinancialInstitutionProvider) :
    FinancialInstitutionService {

    private val map = hashMapOf<String, FinancialInstitution>()

    override fun getInstitutionByCode(financialIdentifier: FinancialIdentifier): Either<FinancialInstitutionError, FinancialInstitution?> {
        if (map.isEmpty()) {
            listFinancialInstitutions()
        }

        return when (financialIdentifier) {
            is FinancialIdentifier.COMPE -> map["compe#${financialIdentifier.id.toString().padStart(3, '0')}"]
            is FinancialIdentifier.ISPB -> map["ispb#${financialIdentifier.id.toString().padStart(8, '0')}"]
        }.right()
    }

    @NewSpan
    @Scheduled(fixedDelay = "10m")
    open fun listFinancialInstitutions(): Either<FinancialInstitutionError, List<FinancialInstitution>> {
        val institutions = provider.listFinancialInstitutions().getOrHandle { return it.left() }

        institutions.forEach { institution ->
            institution.compe?.let { map.putIfAbsent("compe#${it.toString().padStart(3, '0')}", institution) }
            institution.ispb?.let { map.putIfAbsent("ispb#${it.padStart(8, '0')}", institution) }
        }

        return institutions.right()
    }
}

@Singleton
open class DefaultFinancialInstitutionProvider() : FinancialInstitutionProvider {
    override fun listFinancialInstitutions(): Either<FinancialInstitutionError, List<FinancialInstitution>> {
        val content = FileUtils.readLocalFileAsText("institutions.json")

        data class Institution(val name: String, val value: String)
        data class InstitutionList(val results: List<Institution>)

        val list = jacksonObjectMapper().readValue(content, InstitutionList::class.java)
        return list.results.map {
            FinancialInstitution(
                name = it.name,
                ispb = it.value.take(8),
                compe = it.value.takeLast(3).toLongOrNull()
            )
        }.right()
    }
}