package ai.friday.dda.app.interfaces

import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItem
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItemSaveResult
import ai.friday.dda.Err
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.BillRegisterDataListResponse
import ai.friday.dda.app.DdaItem
import ai.friday.dda.app.Document
import ai.friday.dda.app.QueueMessage
import ai.friday.dda.app.QueueMessageBatch
import ai.friday.dda.app.account.AccountId
import ai.friday.dda.app.banking.FinancialInstitution
import ai.friday.dda.app.bill.BarCode
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDAStatus
import arrow.core.Either
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import io.micronaut.context.annotation.Property
import io.micronaut.core.bind.annotation.Bindable
import java.io.InputStream
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import net.javacrumbs.shedlock.core.SimpleLock

const val ddaBillsBean = "dda"
const val activeConcessionariaBillsBean = "active-concessionaria-bills"
const val inactiveConcessionariaBillsBean = "inactive-concessionaria-bills"
const val ddaActivationBean = "dda-activation"
const val ddaBillsImportBean = "dda-bills-import"
const val ddaBillsInternalBean = "dda-bills-internal"
const val ddaOptOutNotification = "dda-opt-out-notification"
const val ddaBillNotificationDlqBean = "dda-bill-notification-dlq"
const val ddaRegisterResponseDlqBean = "dda-register-response-dlq"
const val kafkaRouterBean = "kafka-router"

@EachProperty("aws.sqs.publisher")
class MessagePublisherConfiguration
@ConfigurationInject constructor(
    @param:Parameter val name: String,
    val queueName: String,
    @Property(name = "delay", defaultValue = "0") val delay: Int
)

@EachProperty("aws.sqs.handler")
class MessageHandlerConfiguration
@ConfigurationInject constructor(
    @param:Parameter val name: String,
    val queueName: String,
    @Bindable(defaultValue = "false") val autoScaleWorkersInParallel: Boolean,
    val timeWindowCron: String? = null,
    @Bindable(defaultValue = "300") val timeWindowToleranceInSeconds: Long,
    @Bindable(defaultValue = "1")
    val consumers: Int
)

interface FeatureConfiguration {
    val maintenanceMode: Boolean
}

@ConfigurationProperties("features")
class FeaturesConfigurationMicronaut
@ConfigurationInject constructor(
    override val maintenanceMode: Boolean,
) : FeatureConfiguration

interface DDAProviderService {
    fun getBills(dueDate: LocalDate, document: Document? = null): List<DdaItem>
    fun add(documents: List<Document>)
    fun remove(document: Document)
}

interface DDAProviderServiceV2 : DDAProviderService {
    fun findByCipId(filter: DDARequestFilter.CIPFilter): Either<Err, BillRegisterData>
    fun findByFilter(filter: DDARequestFilter.RangeFilter): Either<Err, BillRegisterDataListResponse>
}

sealed interface FinancialIdentifier {
    data class COMPE(val id: Long) : FinancialIdentifier
    data class ISPB(val id: Long) : FinancialIdentifier
}

interface FinancialInstitutionProvider {
    fun listFinancialInstitutions(): Either<FinancialInstitutionError, List<FinancialInstitution>>
}

interface FinancialInstitutionService {
    fun getInstitutionByCode(financialIdentifier: FinancialIdentifier): Either<FinancialInstitutionError, FinancialInstitution?>
}

sealed interface FinancialInstitutionError {
    object UnknownError : FinancialInstitutionError
}

sealed interface Filters {
    data class DateRange(val start: LocalDate, val end: LocalDate) : Filters
    data class Pagination(val cursor: String? = null, val pageNum: Int = 1, val pageSize: Int = 10) : Filters
}

sealed interface DDARequestFilter {
    data class CIPFilter(val cipID: String) : DDARequestFilter // TODO - encontrar um nome melhor
    data class RangeFilter(
        val dateRange: Filters.DateRange,
        val document: Document,
        val page: Filters.Pagination = Filters.Pagination()
    ) : DDARequestFilter
}

interface MessagePublisher {
    fun sendMessage(queueMessage: QueueMessage): Either<Err, Unit>
    fun sendMessage(queue: String, body: Any, delay: Int? = null): Either<Err, Unit>
    fun sendMessageBatch(messageBatch: QueueMessageBatch)
}

interface ObjectRepository {
    fun listObjectKeys(bucketName: String, directoryKey: String): List<String>
    fun listObjectLastModified(bucketName: String, directoryKey: String): List<ZonedDateTime>
    fun loadObject(bucketName: String, key: String): InputStream
    fun moveObject(bucketName: String, fromKey: String, toKey: String)
    fun uploadObject(bucketName: String, key: String, stream: InputStream)
}

interface InternalLock {
    fun waitForAcquireLock(lockName: String): SimpleLock
    fun acquireLock(
        lockName: String,
        minDuration: Duration? = null,
        maxDuration: Duration? = null,
        simultaneousLock: Int = 1,
    ): SimpleLock?
}

interface DDARepository {
    fun save(ddaRegister: DDARegister): DDARegister
    fun find(accountId: AccountId): DDARegister?
    fun findByStatus(status: DDAStatus): List<DDARegister>
    fun findByDocument(document: String): DDARegister?
}

interface ConcessionariaDiretoRepository {
    fun save(concessionariaDiretoItem: ConcessionariaDiretoItem): ConcessionariaDiretoItemSaveResult
    fun find(barCode: BarCode, dueDate: LocalDate): ConcessionariaDiretoItem?

    fun findLastSeenBillsSince(lastSeen: ZonedDateTime): List<ConcessionariaDiretoItem>
}

data class FileProcessResponseTO(
    val totalItems: Int,
    val processedItems: Int
)