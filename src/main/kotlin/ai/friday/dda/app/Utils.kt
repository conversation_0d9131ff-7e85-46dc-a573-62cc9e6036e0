package ai.friday.dda.app

import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.time.Duration.Companion.seconds

fun isCPF(document: String): Boolean {
    return document.isNotEmpty() && document.length == 11
}

val brazilTimeZone: ZoneId = ZoneId.of("Brazil/East")
val dateFormatWithBrazilTimeZone: DateTimeFormatter = toFormat("yyyy-MM-dd")
val dateTimeFormatWithBrazilTimeZone: DateTimeFormatter = toFormat("yyyy-MM-dd HH:mm:ss")

private fun toFormat(pattern: String) = DateTimeFormatter.ofPattern(pattern).withZone(brazilTimeZone)

object BrazilZonedDateTimeSupplier {
    fun getZonedDateTime(): ZonedDateTime = ZonedDateTime.now(brazilTimeZone)
    fun getLocalDate(): LocalDate = getZonedDateTime().toLocalDate()
}

object DateUtils {
    fun toLocalDate(epochMilli: Long): LocalDate = LocalDate.ofInstant(
        Instant.ofEpochMilli(epochMilli),
        brazilTimeZone
    )

    fun today() = BrazilZonedDateTimeSupplier.getLocalDate()
    fun now() = BrazilZonedDateTimeSupplier.getZonedDateTime()
    fun secondsUntilNow(dateTime: ZonedDateTime) = now().deltaInSeconds(dateTime)
}

fun ZonedDateTime.deltaInSeconds(dateTime: ZonedDateTime) =
    (this.toEpochSecond() - dateTime.toEpochSecond()).seconds.inWholeSeconds

data class QueueMessage(
    val queueName: String,
    val jsonObject: String,
    val delaySeconds: Int? = null,
    val id: String = UUID.randomUUID().toString()
)

data class QueueMessageBatch(val queueName: String, val messages: List<String>, val delaySeconds: Int? = null)