package ai.friday.dda.app

import ai.friday.dda.Err
import ai.friday.dda.app.bill.BarCode
import java.math.BigDecimal
import java.time.LocalDate

data class DdaItem(
    val amount: Double,
    val barcode: BarCode,
    val document: Document,
    val dueDate: LocalDate
)

class DdaProviderLoginException : RuntimeException()

class DdaProviderException : RuntimeException()

data class Document(val value: String)

enum class BillType {
    CONCESSIONARIA,
    FICHA_COMPENSACAO,
    INVOICE,
    PIX,
    OTHERS;

    fun isBankTransfer(): Bo<PERSON>an {
        return this in listOf(INVOICE, PIX)
    }

    fun isBoleto(): Bo<PERSON>an {
        return this in listOf(CONCESSIONARIA, FICHA_COMPENSACAO)
    }
}

enum class AccountType {
    CHECKING,
    SAVINGS,
    SALARY,
    PAYMENT
}

data class BankAccount(
    val accountType: AccountType,
    val bankNo: Long?,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val document: String = "",
    val ispb: String? = null
) {
    fun buildFullAccountNumber() = "$accountNo" + accountDv
}

const val uuidRegexMatcher = "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\$"

enum class PixKeyType(val regex: String) {
    CNPJ("^[0-9]{14}\$"),
    PHONE("^\\+[1-9][0-9]\\d{1,14}\$"),
    EMAIL("^[a-z0-9.!#\$%&‘*+/=?^_`{|}~-]+@[a-z0-9-]+(?:\\.[a-z0-9-]+)*\$"),
    EVP(uuidRegexMatcher),
    CPF("^[0-9]{11}\$")
}

class PixKey(value: String, val type: PixKeyType) {
    val value: String = value.lowercase()

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PixKey

        if (type != other.type) return false
        if (value != other.value) return false

        return true
    }

    override fun hashCode(): Int {
        var result = type.hashCode()
        result = 31 * result + value.hashCode()
        return result
    }
}

data class PixKeyHolder(
    val accountNo: Long,
    val accountDv: String,
    val ispb: String,
    val institutionName: String,
    val accountType: AccountType,
    val routingNo: Long
)

data class PixKeyOwner(
    val name: String,
    val document: String
)

data class PixKeyDetails(
    val key: PixKey,
    val holder: PixKeyHolder,
    val owner: PixKeyOwner
)

data class RecipientChain(
    val sacadorAvalista: Recipient? = null,
    val originalBeneficiary: Recipient? = null,
    val finalBeneficiary: Recipient? = null
)

fun RecipientChain.getRecipientByPayer(
    cpfCnpjPagador: String,
    defaultAssignor: String
): Recipient {
    if (!sacadorAvalista?.document.isNullOrBlank() && !sacadorAvalista!!.document!!.endsWith(cpfCnpjPagador)) {
        return sacadorAvalista
    }

    if (!finalBeneficiary?.document.isNullOrBlank() && !finalBeneficiary!!.document!!.endsWith(cpfCnpjPagador)) {
        return finalBeneficiary
    }

    if (!originalBeneficiary?.document.isNullOrBlank() && !originalBeneficiary!!.document!!.endsWith(cpfCnpjPagador)) {
        return originalBeneficiary
    }

    return Recipient(defaultAssignor)
}

data class Recipient(
    val name: String,
    val document: String? = null,
    val alias: String = "",
    val bankAccount: BankAccount? = null,
    val pixKeyDetails: PixKeyDetails? = null
)

enum class FichaCompensacaoType {
    CH_CHEQUE,
    DM_DUPLICATA_MERCANTIL,
    DMI_DUPLICATA_MERCANTIL_INDICACAO,
    DS_DUPLICATA_DE_SERVICO,
    DSI_DUPLICATA_DE_SERVICO_INDICACAO,
    DR_DUPLICATA_RURAL,
    LC_LETRA_DE_CAMBIO,
    NCC_NOTA_DE_CREDITO_COMERCIAL,
    NCE_NOTA_DE_CREDITO_EXPORTACAO,
    NCI_NOTA_DE_CREDITO_INDUSTRIAL,
    NCR_NOTA_DE_CREDITO_RURAL,
    NP_NOTA_PROMISSORIA,
    NPR_NOTA_PROMISSORIA_RURAL,
    TM_TRIPLICATA_MERCANTIL,
    TS_TRIPLICATA_DE_SERVICO,
    NS_NOTA_DE_SEGURO,
    RC_RECIBO,
    FAT_BLOQUETO,
    ND_NOTA_DE_DEBITO,
    AP_APOLICE_DE_SEGURO,
    ME_MENSALIDADE_ESCOLAR,
    PC_PARCELA_DE_CONSORCIO,
    NF_NOTA_FISCAL,
    DD_DOCUMENTO_DE_DIVIDA,
    CEDULA_DE_PRODUTO_RURAL,
    WARRANT,
    DIVIDA_ATIVA_DE_ESTADO,
    DIVIDA_ATIVA_DE_MUNICIPIO,
    DIVIDA_ATIVA_DA_UNIAO,
    ENCARGOS_CONDOMINIAIS,
    CARTAO_DE_CREDITO,
    BOLETO_PROPOSTA,
    DEPOSITO_E_APORTE,
    OUTROS
}

enum class AmountCalculationModel(val code: String) {
    UNKNOWN(""),
    ANYONE("01"),
    BENEFICIARY_AFTER_DUE_DATE("02"),
    BENEFICIARY_ONLY("03"),
    ON_DEMAND("04");

    companion object {
        fun getByCode(code: String?) = values().firstOrNull() {
            it.code == code
        } ?: UNKNOWN
    }
}

enum class InterestType {
    VALUE,
    PERCENT_BY_DAY,
    PERCENT_BY_MONTH,
    PERCENT_BY_YEAR,
    FREE,
    VALUE_WORKING_DAYS,
    PERCENT_BY_DAY_WORKING_DAYS,
    PERCENT_BY_MONTH_WORKING_DAYS,
    PERCENT_BY_YEAR_WORKING_DAYS,
    UNKNOWN
}

data class InterestData(
    val type: InterestType? = null,
    val value: BigDecimal? = null,
    val date: LocalDate? = null
)

enum class FineType {
    VALUE,
    PERCENT,
    FREE,
    UNKNOWN
}

data class FineData(
    val type: FineType? = null,
    val value: BigDecimal? = null,
    val date: LocalDate? = null
)

enum class DiscountType(val code: String) {
    FREE("0"),
    FIXED_UNTIL_DATE("1"),
    PERCENT_UNTIL_DATE("2"),
    VALUE_BY_DAY("3"),
    VALUE_BY_WORKING_DAY("4"),
    PERCENT_BY_DAY("5"),
    PERCENT_BY_WORKING_DAY("6"),
    UNKNOWN("");

    companion object {
        fun getByCode(code: String): DiscountType {
            return values().firstOrNull() {
                it.code == code
            } ?: UNKNOWN
        }
    }
}

data class DiscountData(
    val type: DiscountType? = null,
    val value1: BigDecimal? = null,
    val date1: LocalDate? = null,
    val value2: BigDecimal? = null,
    val date2: LocalDate? = null,
    val value3: BigDecimal? = null,
    val date3: LocalDate? = null
)

data class DiscountInfo(
    val type: DiscountType? = null,
    val value: Long? = null,
    val date: LocalDate? = null
)

interface PicPayDDAResponse

data class BillRegisterData(
    val barCode: BarCode? = null,
    val billType: BillType,
    val assignor: String,
    val recipient: Recipient? = null,
    val recipientChain: RecipientChain?,
    val payerDocument: String?,
    val amount: Long,
    val discount: Long = 0,
    val interest: Long = 0,
    val fine: Long = 0,
    val amountTotal: Long,
    val expirationDate: LocalDate? = null,
    val dueDate: LocalDate? = null,
    val paidDate: LocalDate? = null,
    val paymentLimitTime: String? = null,
    val settleDate: LocalDate? = null,
    val fichaCompensacaoType: FichaCompensacaoType? = null,
    val payerName: String? = null,
    val amountCalculationModel: AmountCalculationModel,
    val interestData: InterestData? = null,
    val fineData: FineData? = null,
    @Deprecated(
        replaceWith = ReplaceWith("discounts"),
        message = "use discounts version instead"
    )
    val discountData: DiscountData? = null,
    val discounts: List<DiscountInfo>? = null,
    val amountPaid: Long? = null,
    val idNumber: String? = null, // Campo na CIP = NumIdentcTit
    @Deprecated(
        replaceWith = ReplaceWith("rebate"),
        message = "use long version instead"
    )
    val abatement: Double? = null,
    val rebate: Long? = null,
    val paymentStatus: PaymentStatus? = null,
    val billStatus: BillStatus? = null
) : PicPayDDAResponse

data class BillRegisterDataListResponse(
    val totalItems: Int = 0,
    val items: List<BillRegisterData> = emptyList()
) : PicPayDDAResponse {
    fun isEmpty(): Boolean {
        return items.isEmpty()
    }
}

enum class BillStatus {
    ABERTO, BAIXA_EFETIVA_REALIZADA
}

enum class PaymentStatus {
    BAIXA_REALIZADA,
    BLOQUEADO,
    PAGAMENTO_REGISTRADO_EM_BAIXA_OPERACIONAL,
    ENCONTRADO_E_BENEFICIARIO_APTO,
    OUTRO
}

object NotFoundErr : Err("not found error")
object UnexpectedResponse : Err("unexpected response")

class DDAListErr(val page: Int) : Err("error while listing page $page")