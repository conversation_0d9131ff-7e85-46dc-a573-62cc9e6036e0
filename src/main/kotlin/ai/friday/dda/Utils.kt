package ai.friday.dda

import com.fasterxml.jackson.annotation.JsonTypeInfo
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers

internal class FileUtils {
    companion object {
        fun readLocalFileAsText(path: String) = Thread.currentThread().contextClassLoader.getResource(path)?.readText()
        fun readLocalFileAsStream(path: String) = Thread.currentThread().contextClassLoader.getResourceAsStream(path)
    }
}

fun LogstashMarker.andAppend(fieldName: String, `object`: Any?): LogstashMarker {
    return and(Markers.append(fieldName, `object`))
}

suspend fun <A, B> Iterable<A>.parallelMap(f: suspend (A) -> B): List<B> = coroutineScope {
    map { async { f(it) } }.awaitAll()
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "@type",
    visible = true
)
abstract class PrintableSealedClass {
    override fun toString(): String {
        return this::class.simpleName ?: super.toString()
    }
}