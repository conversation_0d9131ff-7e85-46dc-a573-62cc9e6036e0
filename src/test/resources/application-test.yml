##Message: GlobalOpenTelemetry.set has already been called. GlobalOpenTelemetry.set must be called only once
# before any calls to GlobalOpenTelemetry.get. If you are using the OpenTelemetrySdk,
# use OpenTelemetrySdkBuilder.buildAndRegisterGlobal instead. Previous invocation set to cause of this exception.
# Path Taken: new JobManager(List jobs) --> new JobManager([List jobs]) -->
# new $MulticomBillsRetrieverJob$Definition$Intercepted(ObjectRepository objectRepository,
# FTPMulticomAdapter ftpMulticomAdapter,String bucketName,BeanResolutionContext $beanResolutionContext,BeanContext
# $beanContext,Qualifier $qualifier,[List $interceptors]) --> new NewSpanOpenTelemetryTraceInterceptor([Instrumenter instrumenter]) -->
# Instrumenter.instrumenter([OpenTelemetry openTelemetry])
otel.register.global: "false"

micronaut:
  server.ssl.enabled: false
  otel:
    enabled: false

tracing:
  zipkin:
    enabled: false
