{"data": {"bankSlip": {"identificationNumber": "2021010800048576475", "participantControlNumber": "20220815000460862542", "mainReceivingParticipantISPB": "********", "administeredReceivingParticipantISPB": "********", "registrationCurrentReferenceNumber": "1610133905451000108", "registrationUpdateSequenceNumber": "1", "recipientParticipantISPB": "********", "recipientParticipantCode": "341", "originalBeneficiary": {"type": "NATURAL", "document": "***********", "name": "TESTE LISTAR TITULOS", "tradeName": "TESTE LISTAR TITULOS"}, "finalBeneficiary": {"type": "NATURAL", "document": "***********", "name": "TESTE LISTAR TITULOS", "tradeName": "TESTE LISTAR TITULOS"}, "payer": {"type": "NATURAL", "document": "***********", "name": "LISTAR", "tradeName": "LISTAR"}, "guarantorDrawer": {"type": "NATURAL", "document": "***********", "name": "LISTAR", "tradeName": "LISTAR"}, "portfolioCode": "SIMPLE", "cnab": "REAL", "ourNumberIdentifier": "************", "barcodeNumber": "28094839400002000002807664664453548654453693", "typeableBarcodeNumber": "28092807676466445354136544536862483930000200000", "documentNumber": "***********", "expirationDate": "2021-12-30", "amount": 100.0, "speciesCode": "DEPOSIT_CONTRIBUTION_BANK_SLIP", "dispatchDate": "2021-01-08", "numberOfProtestDays": null, "paymentDeadline": "2029-11-30", "paymentType": "IN_CASH", "installmentNumber": null, "numberOfInstallments": null, "negotiatedIndicator": false, "paymentBlockingIndicator": false, "partialPaymentIndicator": false, "partialPaymentAmount": null, "rebateAmount": 10.0, "interest": {"moment": "2022-09-02", "code": "VALUE_CURRENT_DAYS", "percentage": 5.55}, "fine": {"moment": "2022-09-03", "amountType": "PERCENTAGE", "amount": 12.5}, "discounts": [{"moment": "2022-08-01", "amountType": "FIXED_AMOUNT_UNTIL_THE_INFORMED_DATE", "amount": 20.01}], "invoices": null, "minAmountType": null, "minAmount": null, "maxAmountType": null, "maxAmount": null, "calculationModel": "RECEIVING_INSTITUTION_CALCULATES_EXPIRING_BANK_SLIPS_AND_EXPIRED_BANK_SLIPS", "typeOfAuthorizationForReceiptOfDivergentAmount": "ONLY_MINIMUM_AMOUNT", "calculations": null, "beneficiaryInformation": null, "acceptances": null, "thirdParties": null, "situation": "OPEN", "bankSlipSituation": "OPEN", "updatedAt": "2022-08-18T19:54:13.443003", "partialPaymentAmountRegistered": null, "currentTotalPaymentAmount": null, "paymentSituation": "BANK_SLIP_FOUND_IN_THE_CENTRALIZED_BASE_AND_ELIGIBLE_BENEFICIARY_CUSTOMER", "operationalWriteOffs": [{"identificationNumber": "2022091600012957000", "currentReferenceNumber": "1663363457395000916", "updateSequenceNumber": "1", "registrationReferenceNumber": "1663363028016000916", "currentRegistrationReferenceNumber": "1663363028016000916", "type": "WRITE_OFF_INTERBANK", "situation": "PENDING", "updatedAt": "2022-09-16T18:24:17", "details": [{"operationalWriteOffProcessedAt": "2022-09-16T18:24:10", "operationalWriteOffDetailBankSlipAmount": 90.0, "operationalWriteOffBarCodeNumber": "23793911300000020003381260085851017600006330"}], "cancellations": []}], "writeOffs": [{"identificationNumber": "2022091600012957000", "currentReferenceNumber": "1663363457395000916", "updateSequenceNumber": "1", "registrationReferenceNumber": "1663363028016000916", "currentRegistrationReferenceNumber": "1663363028016000916", "type": "WRITE_OFF_INTERBANK", "situation": "PENDING", "updatedAt": "2022-09-16T18:24:17", "details": [{"writeOffProcessedAt": "2022-09-16T18:24:10", "writeOffDetailBankSlipAmount": 90.0, "writeOffBarCodeNumber": "23793911300000020003381260085851017600006330"}], "cancellations": []}, {"identificationNumber": "2305290610093660887", "currentRegistrationReferenceNumber": "1685388143042000529", "receivingParticipantISPB": "********", "receivingParticipantCode": "380", "type": "WRITE_OFF_INTRA_BANK", "situation": "EFFECTIVE", "details": [{"bankSlipReceiveAt": "2023-05-29T15:52", "writeOffProcessedAt": "2023-05-29T15:52", "writeOffDetailBankSlipAmount": 30.0, "writeOffBarCodeNumber": "38093800400000100000123456789012745678012324", "writeOffPaymentChannel": "AGENCY", "writeOffPaymentMethod": "IN_CASH", "contingencyOperationIndicator": false}]}], "effectiveWriteOffs": null}}}