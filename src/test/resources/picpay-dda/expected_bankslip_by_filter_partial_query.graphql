{"query": "query bankSlips ($filter: BankSlipFilter) {    bankSlips (filter: $filter) {        totalItems        items {            identificationNumber            participantControlNumber            mainReceivingParticipantISPB            administeredReceivingParticipantISPB            registrationCurrentReferenceNumber            registrationUpdateSequenceNumber            recipientParticipantISPB            recipientParticipantCode            originalBeneficiary {                type                document                name                tradeName            }            finalBeneficiary {                type                document                name                tradeName            }            payer {                type                document                name                tradeName            }            guarantorDrawer {                type                document                name                tradeName            }            portfolioCode            cnab            ourNumberIdentifier            barcodeNumber            typeableBarcodeNumber            documentNumber            expirationDate            amount            speciesCode            dispatchDate            numberOfProtestDays            paymentDeadline            paymentType            installmentNumber            numberOfInstallments            negotiatedIndicator            paymentBlockingIndicator            partialPaymentIndicator            partialPaymentAmount            rebateAmount            interest {                moment                code                percentage            }            fine {                moment                amountType                amount            }            discounts {                moment                amountType                amount            }            invoices {                number                issuedAt                amount            }            minAmountType            minAmount            maxAmountType            maxAmount            calculationModel            typeOfAuthorizationForReceiptOfDivergentAmount            calculations {                interestAmount                fineAmount                discountAmount                totalAmount                dueDate            }            beneficiaryInformation            acceptances {                referenceNumber                sequenceNumber                indicator            }            thirdParties {                identificationNumber                registrationCurrentReferenceNumber                authorizerPayerPersonType                authorizerPayerDocument                personType                document                situation            }            situation            updatedAt            partialPaymentAmountRegistered            currentTotalPaymentAmount            paymentSituation            writeOffs {                identificationNumber                currentReferenceNumber                updateSequenceNumber                registrationReferenceNumber                currentRegistrationReferenceNumber                receivingParticipantISPB                receivingParticipantCode                type                situation                updatedAt                details {                    carrierPersonType                    carrierDocument                    carrierName                    bankSlipReceiveAt                    writeOffProcessedAt                    writeOffDetailBankSlipAmount                    writeOffBarCodeNumber                    writeOffPaymentChannel                    writeOffPaymentMethod                    contingencyOperationIndicator                }            }            operationalWriteOffs {                identificationNumber                currentReferenceNumber                updateSequenceNumber                registrationReferenceNumber                currentRegistrationReferenceNumber                receivingParticipantISPB                receivingParticipantCode                type                situation                updatedAt                details {                    carrierPersonType                    carrierDocument                    carrierName                    bankSlipReceiveAt                    operationalWriteOffProcessedAt                    operationalWriteOffDetailBankSlipAmount                    operationalWriteOffBarCodeNumber                    operationalWriteOffPaymentChannel                    operationalWriteOffPaymentMethod                    contingencyOperationIndicator                }                cancellations {                    canceledAt                }            }            effectiveWriteOffs {                identificationNumber                currentReferenceNumber                updateSequenceNumber                operationalWriteOffIdentificationNumber                type                receivingParticipantISPB                receivingParticipantCode                processingDateAndTime                valueOfTheEffectiveWriteOffOfTheBankSlip                barCode                paymentChannel                paymentMethod                momentOfEffectiveWriteOffStatus            }        }    }}", "variables": {"filter": {"payer": {"type": "NATURAL", "document": "***********"}, "range": {"ini": 50, "fin": 100}}}}