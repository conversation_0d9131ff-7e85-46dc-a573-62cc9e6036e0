package archunit

import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses

const val namespace = "ai.friday.dda"

@AnalyzeClasses(packages = [namespace], importOptions = [ImportOption.DoNotIncludeTests::class])
internal class HexagonalTest {

    @ArchTest
    fun testHexagonal(importedClasses: JavaClasses) {
        noClasses().that()
            .resideInAPackage("$namespace.app.(*)")
            .should().dependOnClassesThat().resideInAPackage("$namespace.adapters.(*)")
            .check(importedClasses)
    }

//    @ArchTest
//    @Disabled
//    fun `micronaut dependencies should not reside inside domain files`(importedClasses: JavaClasses) {
//        noClasses()
//            .that().resideInAPackage("$namespace.app.(*)")
//            .should()
//            .dependOnClassesThat().resideInAnyPackage("..micronaut..")
//            .check(importedClasses)
//    }
}