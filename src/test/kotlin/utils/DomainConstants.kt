package utils

class DomainConstants {
    companion object {
        const val EMAIL = "<EMAIL>"
        const val EMAIL_2 = "<EMAIL>"
        const val NAME = "Fulano Beltrano"
        const val ACCOUNT_ID = "ACCOUNT-4828265b-6fee-4101-8b3a-3aa039891499"
        const val ACCOUNT_ID_2 = "ACCOUNT-9e8f8d4c-0a1f-3d4f-ac30-40659849c9e8"
        const val DOCUMENT = "***********"
        const val FORMATTED_DOCUMENT = "088.458.567-04"
        const val DOCUMENT_2 = "***********"
        const val DOCUMENT_3 = "***********"
        const val CNPJ_1 = "**************"

        const val CONCESSIONARIA_DIGITABLE_LINE = "924700000013367102962011909100180007002472014444"
        const val FICHA_DE_COMPENSACAO_DIGITABLE_LINE = "00190000090280340600421010889174581950000005897"
        const val FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2 = "00190000090286665907712371179172980510000002500"
        const val FICHA_DE_COMPENSACAO_BARCODE = "00195819500000058970000002803406002101088917"
        const val PREFECTURE_BARCODE_LINE = "816800000009857900531073096493867111101007954817"
        const val SANITATION_BARCODE_LINE = "826800000009857900531073096493867111101007954817"
        const val ELECTRICITY_AND_GAS_BARCODE_LINE = "836800000009857900531073096493867111101007954817"
        const val TELECOM_BARCODE_LINE = "846600000018717202962022103100380007002815015645"
        const val GOVERNMENT_BARCODE_LINE = "856800000009857900531073096493867111101007954817"
        const val CARNET_BARCODE_LINE = "866800000009857900531073096493867111101007954817"
        const val TRAFFIC_TICKET_BARCODE_LINE = "876800000009857900531073096493867111101007954817"

        const val DIGITABLE_LINE = "236100000013559102962011909100380001002373072615"
    }
}