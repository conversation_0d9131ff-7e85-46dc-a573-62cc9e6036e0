package utils

import ai.friday.dda.FileUtils
import ai.friday.dda.adapters.picpay.BankSlip
import ai.friday.dda.adapters.picpay.BankSlipWriteOff
import ai.friday.dda.adapters.picpay.BankSlipWriteOffType
import ai.friday.dda.adapters.picpay.Calculation
import ai.friday.dda.adapters.picpay.OperationalWriteOffRequestSituation
import ai.friday.dda.adapters.picpay.PaymentChannel
import ai.friday.dda.adapters.picpay.PaymentMethod
import ai.friday.dda.adapters.picpay.PersonType
import ai.friday.dda.adapters.picpay.WriteOffDetail
import ai.friday.dda.app.interfaces.FinancialIdentifier
import ai.friday.dda.app.interfaces.FinancialInstitutionService
import ai.friday.dda.mapper
import arrow.core.Either
import arrow.core.right
import io.mockk.every
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class PicPayDDAFixture {
    companion object {
        private val BANK_SLIP_JSON: String = FileUtils.readLocalFileAsText("picpay-dda/bankslip.json")
            ?: throw IllegalStateException("bankslip.json not found")

        val BANK_SLIP: BankSlip = mapper.readValue(BANK_SLIP_JSON, BankSlip::class.java)

        const val EXPIRATION_DATE = "2022-10-01"
        const val DUE_DATE = "2022-08-10"
        const val DUE_DATE_START = "2022-08-09"
        const val DUE_DATE_END = "2022-08-31"
        const val INTEREST_DATE = "2022-09-02"
        const val FINE_DATE = "2022-09-03"

        val BANK_SLIP_WRITE_OFF_DETAIL = WriteOffDetail(
            carrierPersonType = PersonType.LEGAL,
            carrierDocument = "********",
            carrierName = "Carrier",
            bankSlipReceiveAt = LocalDate.parse("2022-09-03").atTime(11, 33, 44),
            writeOffProcessedAt = null,
            writeOffDetailBankSlipAmount = BigDecimal.TEN,
            writeOffBarCodeNumber = DomainConstants.FICHA_DE_COMPENSACAO_BARCODE,
            writeOffPaymentChannel = PaymentChannel.INTERNET,
            writeOffPaymentMethod = PaymentMethod.IN_CASH,
            contingencyOperationIndicator = null
        )

        val BANK_SLIP_WRITE_OFF = BankSlipWriteOff(
            identificationNumber = "123",
            currentReferenceNumber = "1234",
            updateSequenceNumber = "12345",
            registrationReferenceNumber = "123456",
            currentRegistrationReferenceNumber = "1234567",
            receivingParticipantISPB = DomainFixture.ITAU.ispb,
            receivingParticipantCode = DomainFixture.ITAU.compe.toString(),
            updatedAt = LocalDateTime.parse("2022-09-01T13:18:01"),
            type = BankSlipWriteOffType.WRITE_OFF_INTERBANK,
            situation = OperationalWriteOffRequestSituation.EFFECTIVE,
            details = listOf(
                BANK_SLIP_WRITE_OFF_DETAIL,
                BANK_SLIP_WRITE_OFF_DETAIL.copy(writeOffDetailBankSlipAmount = BigDecimal.TEN)
            ),
            cancellations = listOf()
        )

        val institutionService = mockk<FinancialInstitutionService> {
            every { getInstitutionByCode(FinancialIdentifier.COMPE(0)) } returns Either.Right(null)
            every { getInstitutionByCode(FinancialIdentifier.COMPE(341)) } returns DomainFixture.ITAU.right()
            every { getInstitutionByCode(FinancialIdentifier.COMPE(237)) } returns DomainFixture.BRADESCO.right()
        }

        fun buildCalculation(
            totalAmount: Long? = null,
            discountAmount: Long? = null,
            fineAmount: Long? = null,
            interestAmount: Long? = null,
            dueDate: LocalDate? = null
        ) = Calculation(
            totalAmount = totalAmount?.let { BigDecimal.valueOf(totalAmount) },
            discountAmount = discountAmount?.let { BigDecimal.valueOf(discountAmount) },
            fineAmount = fineAmount?.let { BigDecimal.valueOf(fineAmount) },
            interestAmount = interestAmount?.let { BigDecimal.valueOf(interestAmount) },
            dueDate = dueDate
        )
    }

    object Query {
        val EXPECTED_COMPLETE_BANK_SLIP_FILTERED_QUERY =
            FileUtils.readLocalFileAsText("picpay-dda/expected_bankslip_by_filter_complete_query.graphql")
                ?: throw IllegalStateException("expected_bankslip_by_filter_complete_query.graphql not found")

        val EXPECTED_PARTIAL_BANK_SLIP_FILTERED_QUERY =
            FileUtils.readLocalFileAsText("picpay-dda/expected_bankslip_by_filter_partial_query.graphql")
                ?: throw IllegalStateException("expected_bankslip_by_filter_partial_query.graphql not found")

        val EXPECTED_BANK_SLIP_QUERY = FileUtils.readLocalFileAsText("picpay-dda/expected_bankslip_by_id_query.graphql")
            ?: throw IllegalStateException("expected_bankslip_by_id_query.graphql not found")
    }
}