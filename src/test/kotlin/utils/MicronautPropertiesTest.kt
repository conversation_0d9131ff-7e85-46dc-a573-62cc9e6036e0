package utils

import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@PropertySource(
    Property(name = "application.reagion", value = "foo"),
    Property(name = "lock.configuration.shedlock-job.maxDuration", value = "1m"),
    Property(name = "lock.configuration.shedlock-job.minDuration", value = "0s"),
    Property(name = "lock.configuration.shedlock-job.prefix", value = "FOO#"),
    Property(name = "features.maintenance-mode", value = "false"),
    Property(name = "features.maintenance-services", value = "false"),
    Property(name = "shedlock.defaults.lock-at-most-for", value = "30m"),
    Property(name = "integrations.concessionaria.multicom.ftp.host", value = "host"),
    Property(name = "integrations.concessionaria.multicom.ftp.username", value = "username"),
    Property(name = "integrations.concessionaria.multicom.ftp.key", value = "key"),
    Property(name = "integrations.concessionaria.multicom.ftp.passphrase", value = "passphrase"),
    Property(name = "integrations.concessionaria.multicom.bucketName", value = "bucket_name"),
    Property(name = "micronaut.server.port", value = "-1"),
    Property(name = "micronaut.server.ssl.port", value = "-1"),
    Property(name = "aws.sqs.publisher.active-concessionaria-bills.queue-name", value = "active-concessionaria-bills"),
    Property(
        name = "aws.sqs.publisher.inactive-concessionaria-bills.queue-name",
        value = "inactive-concessionaria-bills"
    ),
    Property(name = "aws.sqs.publisher.dda-bills-import.queue-name", value = "dda-bills-import"),
)
annotation class MicronautPropertiesTest