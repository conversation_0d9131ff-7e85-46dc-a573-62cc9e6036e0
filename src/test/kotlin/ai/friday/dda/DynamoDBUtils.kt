package ai.friday.dda

import ai.friday.dda.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.dda.adapters.dynamodb.DDAEntity
import ai.friday.dda.adapters.dynamodb.DDA_SERVICE_PARTITION_KEY
import ai.friday.dda.adapters.dynamodb.DDA_SERVICE_RANGE_KEY
import ai.friday.dda.adapters.dynamodb.DDA_SERVICE_TABLE_NAME
import ai.friday.dda.adapters.dynamodb.PARTITION_KEY
import ai.friday.dda.adapters.dynamodb.RANGE_KEY
import ai.friday.dda.adapters.dynamodb.SHEDLOCK_TABLE_NAME
import ai.friday.dda.integration.LocalDbCreationRule
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest
import software.amazon.awssdk.services.dynamodb.model.CreateTableResponse
import software.amazon.awssdk.services.dynamodb.model.DeleteTableRequest
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest
import software.amazon.awssdk.services.dynamodb.model.GlobalSecondaryIndex
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement
import software.amazon.awssdk.services.dynamodb.model.KeyType
import software.amazon.awssdk.services.dynamodb.model.Projection
import software.amazon.awssdk.services.dynamodb.model.ProjectionType
import software.amazon.awssdk.services.dynamodb.model.ProvisionedThroughput
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType
import software.amazon.awssdk.services.dynamodb.model.TableStatus

object DynamoDBUtils {
    data class DynamoClis(
        val syncCli: DynamoDbClient,
        val asyncCli: DynamoDbAsyncClient,
        val enhancedClient: DynamoDbEnhancedClient,
        val enhancedAsyncClient: DynamoDbEnhancedAsyncClient,
    )

    private var clis: DynamoClis? = null

    fun setup(): DynamoClis {
        if (this.clis != null) return this.clis!!

        LocalDbCreationRule.startServer()

        val (basicSync, basicAsync) = LocalDbCreationRule.dynamoDB.dynamoDbClient() to LocalDbCreationRule.dynamoDB.dynamoDbAsyncClient()
        val (enhancedSync, enhancedAsync) = this.createEnhancedCli(basicSync, basicAsync)

        this.createBillPaymentTable(basicSync)
        this.createDDAServiceTable(basicSync)
        this.createShedlockTable(basicSync)

        this.clis = DynamoClis(basicSync, basicAsync, enhancedSync, enhancedAsync)

        return this.clis!!
    }

    private fun createEnhancedCli(
        basicSync: DynamoDbClient,
        basicAsync: DynamoDbAsyncClient,
    ): Pair<DynamoDbEnhancedClient, DynamoDbEnhancedAsyncClient> {
        val enhancedSync = DynamoDbEnhancedClient.builder()
            .dynamoDbClient(basicSync)
            .build()

        val enhancedAsync = DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(basicAsync)
            .build()

        return enhancedSync to enhancedAsync
    }

    fun setupDynamoDB(): DynamoDbEnhancedClient {
        if (this.clis == null) this.clis = setup()

        return this.clis!!.enhancedClient
    }

    fun createDDAServiceTable(dynamoDbClient: DynamoDbClient) {
        createTable(
            dynamoDbClient,
            DDA_SERVICE_TABLE_NAME,
            DDA_SERVICE_PARTITION_KEY,
            DDA_SERVICE_RANGE_KEY,
            "GSIndex1PartitionKey",
            "GSIndex1RangeKey"
        )
    }

    fun createBillPaymentTable(dynamoDbClient: DynamoDbClient) {
        createTable(
            dynamoDbClient,
            BILL_PAYMENT_TABLE_NAME,
            PARTITION_KEY,
            RANGE_KEY,
            "GSIndex1PrimaryKey",
            "GSIndex1ScanKey",
            "GSIndex2PrimaryKey",
            "GSIndex2ScanKey",
            "GSIndex3PrimaryKey",
            "GSIndex3ScanKey"
        )
    }

    fun createShedlockTable(dynamoDbClient: DynamoDbClient) {
        net.javacrumbs.shedlock.provider.dynamodb2.DynamoDBUtils.createLockTable(
            dynamoDbClient,
            SHEDLOCK_TABLE_NAME,
            ProvisionedThroughput.builder().readCapacityUnits(1_000).writeCapacityUnits(1_000).build()
        )
    }

    private fun createTable(
        client: DynamoDbClient,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String? = null,
        gsi2RangeKeyName: String? = null,
        gsi3HashKeyName: String? = null,
        gsi3RangeKeyName: String? = null
    ): CreateTableResponse {
        val attributeDefinitions = mutableListOf(
            AttributeDefinition.builder().attributeName(hashKeyName).attributeType(ScalarAttributeType.S).build(),
            AttributeDefinition.builder().attributeName(rangeKeyName).attributeType(ScalarAttributeType.S).build(),
            AttributeDefinition.builder().attributeName(gsi1HashKeyName).attributeType(ScalarAttributeType.S).build(),
            AttributeDefinition.builder().attributeName(gsi1RangeKeyName).attributeType(ScalarAttributeType.S).build()
        )
        gsi2HashKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build()
            )
        }
        gsi2RangeKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build()
            )
        }
        gsi3HashKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build()
            )
        }
        gsi3RangeKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build()
            )
        }
        return createTable(
            client,
            tableName,
            hashKeyName,
            rangeKeyName,
            gsi1HashKeyName,
            gsi1RangeKeyName,
            gsi2HashKeyName,
            gsi2RangeKeyName,
            gsi3HashKeyName,
            gsi3RangeKeyName,
            attributeDefinitions
        )
    }

    private fun createTable(
        client: DynamoDbClient,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String?,
        gsi2RangeKeyName: String?,
        gsi3HashKeyName: String?,
        gsi3RangeKeyName: String?,
        attributeDefinitions: List<AttributeDefinition>
    ): CreateTableResponse {
        try {
            client.deleteTable(DeleteTableRequest.builder().tableName(tableName).build())
        } catch (e: ResourceNotFoundException) {
        }
        val ks = listOf(
            KeySchemaElement.builder().attributeName(hashKeyName).keyType(KeyType.HASH).build(),
            KeySchemaElement.builder().attributeName(rangeKeyName).keyType(KeyType.RANGE).build()
        )
        val globalSecondaryIndexes: MutableList<GlobalSecondaryIndex> = ArrayList()
        addIndex(
            globalSecondaryIndexes,
            ai.friday.dda.adapters.dynamodb.GlobalSecondaryIndex.GSIndex1,
            gsi1HashKeyName,
            gsi1RangeKeyName
        )
        addIndex(
            globalSecondaryIndexes,
            ai.friday.dda.adapters.dynamodb.GlobalSecondaryIndex.GSIndex2,
            gsi2HashKeyName,
            gsi2RangeKeyName
        )
        addIndex(
            globalSecondaryIndexes,
            ai.friday.dda.adapters.dynamodb.GlobalSecondaryIndex.GSIndex3,
            gsi3HashKeyName,
            gsi3RangeKeyName
        )
        val provisionedThroughput =
            ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build()
        val request = CreateTableRequest.builder()
            .tableName(tableName)
            .attributeDefinitions(attributeDefinitions)
            .keySchema(ks)
            .globalSecondaryIndexes(globalSecondaryIndexes)
            .provisionedThroughput(provisionedThroughput).build()

        val result = client.createTable(request)

        val describeTableRequest = DescribeTableRequest.builder().tableName(tableName).build()
        val describeTableResponse = client.describeTable(describeTableRequest)

        while (describeTableResponse.table().tableStatus() != TableStatus.ACTIVE) {
            // wait til table is active or otherwise will not find table
        }
        return result
    }

    private fun addIndex(
        globalSecondaryIndexes: MutableList<GlobalSecondaryIndex>,
        index: ai.friday.dda.adapters.dynamodb.GlobalSecondaryIndex,
        partitionKeyName: String?,
        rangeKeyName: String?
    ) {
        if (partitionKeyName != null) {
            val gsi = listOf(
                KeySchemaElement.builder().attributeName(partitionKeyName).keyType(KeyType.HASH).build(),
                KeySchemaElement.builder().attributeName(rangeKeyName).keyType(KeyType.RANGE).build()
            )
            globalSecondaryIndexes.add(
                GlobalSecondaryIndex.builder()
                    .keySchema(gsi)
                    .indexName(index.name)
                    .projection(Projection.builder().projectionType(ProjectionType.ALL).build())
                    .provisionedThroughput(
                        ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build()
                    ).build()
            )
        }
    }

    fun putItem(client: DynamoDbEnhancedClient, tableName: String, item: DDAEntity) {
        val table = client.table(tableName, TableSchema.fromBean(DDAEntity::class.java))
        table.putItem(item)
    }

    inline fun <reified T> putItem(cli: DynamoDbEnhancedClient, tableName: String, vararg items: T) {
        val table = cli.table(tableName, TableSchema.fromBean(T::class.java))
        items.forEach(table::putItem)
    }
//
//    fun getItem(client: AmazonDynamoDB?, tableName: String?, primaryKey: String?, scanKey: String?): Item {
//        val dynamoDB = DynamoDB(client)
//        val table = dynamoDB.getTable(tableName)
//        return table.getItem(BILL_PAYMENT_PARTITION_KEY, primaryKey, BILL_PAYMENT_RANGE_KEY, scanKey)
//    }
//
//    fun removeItem(client: AmazonDynamoDB?, tableName: String?, hashKey: String?, rangeKey: String?) {
//        val dynamoDB = DynamoDB(client)
//        val table = dynamoDB.getTable(tableName)
//        table.deleteItem(BILL_PAYMENT_PARTITION_KEY, hashKey, BILL_PAYMENT_RANGE_KEY, rangeKey)
//    }
}