package ai.friday.dda

import ai.friday.dda.adapters.picpay.PersonType
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEvent
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventBankSlip
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventPayer
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.BillRegisterDataListResponse

object DDAEventFixture {
    fun createEvent(
        document: String = "document",
        cipID: String = "cip_id",
        type: String = "NEW"
    ) =
        PicPayDDAEvent(
            payer = createPayer(document),
            bankSlip = createBankSlip(cipID),
            id = "external-request-id",
            type = type
        )

    fun createPayer(document: String) = PicPayDDAEventPayer(type = PersonType.NATURAL, document = document)

    fun createBankSlip(cipID: String) = PicPayDDAEventBankSlip(id = cipID, "2022-01-01 10:00")

    fun billDataResponse(
        items: MutableList<BillRegisterData>,
        fromIndex: Int = 0,
        toIndex: Int = 0
    ) =
        BillRegisterDataListResponse(
            totalItems = items.size,
            items = if (fromIndex == 0 || toIndex == 0) items else items.subList(fromIndex, toIndex)
        )
}