package ai.friday.dda.adapters.arbi

import ai.friday.dda.app.Document
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.core.spec.style.AnnotationSpec
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.http.client.HttpClient
import io.mockk.CapturingSlot
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.time.LocalDate
import utils.DomainConstants

internal class ArbiAdapterTest : AnnotationSpec() {

    private val mockBlockingClient: BlockingHttpClient = mockk(relaxed = true)

    private val mockClient: HttpClient = mockk(relaxed = true) {
        every { toBlocking() } returns mockBlockingClient
    }

    private val configuration = ArbiConfiguration().apply {
        host = ""
        grantCodePath = ""
        accessTokenPath = ""
        validatePath = ""
        checkingPath = ""
        ddaPath = ""
        domainPath = ""
        contaCashin = ""
        userToken = ""
        clientId = ""
        clientSecret = ""
        contaTitular = "8745632"
        inscricao = "12345678000123"
        tipoPessoa = "J"
        ddaCadastroPath = ""
        paymentTimeLimit = 18
    }

    private val arbiAdapter =
        ArbiAdapter(
            httpClient = mockClient,
            configuration = configuration,
            authenticationManager = mockk(relaxed = true)
        )

    @BeforeEach
    fun setup() {
        every {
            mockBlockingClient.retrieve(
                any<HttpRequest<GrantCodeTO>>(),
                Argument.of(GrantCodeResponseTO::class.java)
            )
        } answers {
            GrantCodeResponseTO("http://localhost/?code=XXXX_GRANT_CODE_XXXX")
        }

        every {
            mockBlockingClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java)
            )
        } answers {
            AccessTokenTO(
                accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                refreshToken = "XXXX_REFRESH_TOKEN_XXXX",
                expiresIn = 365,
                tokenType = "ACCESS_TOKEN"
            )
        }
    }

    @Test
    fun `should get bills from friday account with document`() {
        val queryDate = LocalDate.of(2021, 11, 1)

        val httpRequestSlot = slot<HttpRequest<DDATO>>()

        mockDDaResponseTO(httpRequestSlot)

        val bills =
            arbiAdapter.getBills(queryDate, Document("***********"))

        bills.shouldNotBeEmpty()

        val httpRequest = httpRequestSlot.captured

        with(httpRequest.body.get().dda) {
            contaTitular shouldBe configuration.contaTitular
            inscricao shouldBe "***********"
            tipoPessoa shouldBe "F"
            dataVctoTitulo shouldBe queryDate.format(vctoTituloDateFormat)
        }
    }

    @Test
    fun `should get bills from friday account`() {
        val queryDate = LocalDate.of(2021, 11, 1)

        val httpRequestSlot = slot<HttpRequest<DDATO>>()

        mockDDaResponseTO(httpRequestSlot)

        val bills =
            arbiAdapter.getBills(queryDate)

        bills.shouldNotBeEmpty()

        val httpRequest = httpRequestSlot.captured

        with(httpRequest.body.get().dda) {
            contaTitular shouldBe configuration.contaTitular
            inscricao shouldBe configuration.inscricao
            tipoPessoa shouldBe "J"
            dataVctoTitulo shouldBe queryDate.format(vctoTituloDateFormat)
        }
    }

    private fun mockDDaResponseTO(httpRequestSlot: CapturingSlot<HttpRequest<DDATO>>) {
        every {
            mockBlockingClient.retrieve(
                capture(httpRequestSlot),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING
            )
        } answers {
            listOf(buildArbiResponseTO())
        }
    }

    private fun buildArbiResponseTO(): ArbiResponseTO {
        val result = jacksonObjectMapper().writeValueAsString(
            mutableMapOf(
                "valor" to "1234",
                "codbarras" to DomainConstants.FICHA_DE_COMPENSACAO_BARCODE,
                "linhadigitavel" to DomainConstants.FICHA_DE_COMPENSACAO_DIGITABLE_LINE,
                "inscricaosacado" to "12345678909",
                "datavctotitulo" to "01/12/2021"
            )
        )

        return ArbiResponseTO(
            idTransacao = 2,
            resultado = result,
            idStatus = 201,
            idRequisicaoParceiro = "fakeId",
            idModulo = 1,
            idRequisicaoArbi = "mock",
            descricaoStatus = "Sucesso"
        )
    }
}