package ai.friday.dda.adapters.arbi

import ai.friday.dda.app.interfaces.FileProcessResponseTO
import ai.friday.dda.app.tenant.TenantName
import com.fasterxml.jackson.core.JsonParseException
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.io.FileInputStream
import org.junit.jupiter.api.assertThrows

internal class DDABillsBatchProcessorTest : FunSpec({

    val ddaFilterServiceMock: DDAFilterService = mockk()
    val sendMessageProcessor: SendMessageProcessor = mockk(relaxed = true)
    val processor = DDABillsBatchProcessor(ddaFilterServiceMock, sendMessageProcessor)
    val tenantName = TenantName("FRIDAY")

    beforeEach {
        clearMocks(ddaFilterServiceMock)
        every { ddaFilterServiceMock.filter(any(), any()) } returns true
    }

    test("deve jogar excecao com arquivo num formato invalido") {
        val resource = Thread.currentThread().contextClassLoader.getResource("files/Invalid_File.json")
        assertThrows<JsonParseException> {
            processor.process(FileInputStream(resource!!.path), tenantName)
        }
    }

    test("deve processar arquivos com zero registros") {
        val resource = Thread.currentThread().contextClassLoader.getResource("files/Empty_File.json")
        processor.process(FileInputStream(resource!!.path), tenantName)
        verify {
            sendMessageProcessor wasNot called
        }
    }

    test("deve processar arquivo com um registro") {
        val resource = Thread.currentThread().contextClassLoader.getResource("files/DDAInformation.json")
        val result = processor.process(FileInputStream(resource!!.path), tenantName)

        result shouldBe FileProcessResponseTO(
            totalItems = 1,
            processedItems = 1
        )

        verify(exactly = 1) {
            sendMessageProcessor.process(any(), any())
            ddaFilterServiceMock.filter(any(), any())
        }
    }

    test("não deve enviar dda para fila quando não passar no filtro de duplicados") {
        val resource = Thread.currentThread().contextClassLoader.getResource("files/DDAInformation.json")

        every { ddaFilterServiceMock.filter(any(), any()) } returns false

        val result = processor.process(FileInputStream(resource!!.path), tenantName)

        result shouldBe FileProcessResponseTO(
            totalItems = 1,
            processedItems = 0
        )

        verify(exactly = 1) {
            ddaFilterServiceMock.filter(any(), any())
        }
        verify(exactly = 0) {
            sendMessageProcessor.process(any(), any())
        }
    }

    test("deve processar arquivo com um registro com codmora e codmulta null") {
        val resource = Thread.currentThread().contextClassLoader.getResource("files/DDAInformationWithoutCodmora.json")
        processor.process(FileInputStream(resource!!.path), tenantName)
        val slot = slot<List<DDABillTO>>()

        verify(exactly = 1) {
            sendMessageProcessor.process(capture(slot), any())
        }

        with(slot.captured.first()) {
            this.codmora shouldBe "5"
            this.codmulta shouldBe "3"
        }
    }

    test("deve remover baixas efetivas duplicadas") {
        val resource = Thread.currentThread().contextClassLoader.getResource("files/DDA_Com_Baixa_Duplicada.json")
        val result = processor.process(FileInputStream(resource!!.path), tenantName)

        val ddaBill = slot<DDABillTO>()

        result shouldBe FileProcessResponseTO(
            totalItems = 1,
            processedItems = 1
        )

        verify(exactly = 1) {
            ddaFilterServiceMock.filter(capture(ddaBill), any())
        }

        ddaBill.captured.baixasEfetivas.size shouldBe 40
    }

    afterTest {
        clearMocks(sendMessageProcessor)
    }
})