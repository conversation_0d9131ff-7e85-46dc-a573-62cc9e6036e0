package ai.friday.dda.adapters.arbi

import ai.friday.dda.app.QueueMessageBatch
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.app.tenant.ResponseConfiguration
import ai.friday.dda.app.tenant.TenantConfiguration
import ai.friday.dda.app.tenant.TenantName
import ai.friday.dda.mapper
import io.kotest.core.spec.style.FunSpec
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

internal class SendMessageProcessorTest : FunSpec({

    val messagePublisher: MessagePublisher = mockk(relaxed = true)
    val sendDdaBillAdapter: SendDdaBillAdapter = mockk(relaxed = true)

    val httpConfiguration = ResponseConfiguration.HttpConfiguration(
        username = "testUser",
        password = "testPass"
    )

    val messageConfiguration = ResponseConfiguration.MessageConfiguration(
        queueName = "test-queue"
    )

    val fridayTenantConfig: TenantConfiguration = mockk {
        every { tenantName } returns TenantName("FRIDAY")
        every { configuration } returns messageConfiguration
    }

    val motorolaTenantConfig: TenantConfiguration = mockk {
        every { tenantName } returns TenantName("MOTOROLA")
        every { configuration } returns httpConfiguration
    }

    val tenantConfigurations = listOf(fridayTenantConfig, motorolaTenantConfig)

    val processor = SendMessageProcessor(
        configuration = tenantConfigurations,
        messagePublisher = messagePublisher,
        sendDdaBill = sendDdaBillAdapter
    )

    val sampleDDABillTO = mockk<DDABillTO>(relaxed = true)

    beforeEach {
        clearMocks(messagePublisher, sendDdaBillAdapter)
    }

    context("process method") {
        test("should send via HTTP when tenant has HttpConfiguration") {
            val ddaBills = listOf(sampleDDABillTO)

            processor.process(ddaBills, TenantName("MOTOROLA"))

            verify(exactly = 1) {
                sendDdaBillAdapter.send(ddaBills, TenantName("MOTOROLA"))
            }

            verify(exactly = 0) {
                messagePublisher.sendMessageBatch(any())
            }
        }

        test("should send via message queue when tenant has MessageConfiguration") {
            val ddaBills = listOf(sampleDDABillTO)
            val expectedJson = mapper.writeValueAsString(sampleDDABillTO)

            processor.process(ddaBills, TenantName("FRIDAY"))

            verify(exactly = 1) {
                messagePublisher.sendMessageBatch(
                    QueueMessageBatch(
                        queueName = "test-queue",
                        messages = listOf(expectedJson)
                    )
                )
            }
            verify(exactly = 0) {
                sendDdaBillAdapter.send(any(), any())
            }
        }

        test("should handle multiple DDA bills with MessageConfiguration") {
            val ddaBill1 = sampleDDABillTO.copy(numidentcdda = 111L)
            val ddaBill2 = sampleDDABillTO.copy(numidentcdda = 222L)
            val ddaBills = listOf(ddaBill1, ddaBill2)

            val expectedJson1 = mapper.writeValueAsString(ddaBill1)
            val expectedJson2 = mapper.writeValueAsString(ddaBill2)

            processor.process(ddaBills, TenantName("FRIDAY"))

            verify(exactly = 1) {
                messagePublisher.sendMessageBatch(
                    QueueMessageBatch(
                        queueName = "test-queue",
                        messages = listOf(expectedJson1, expectedJson2)
                    )
                )
            }
        }
    }
})