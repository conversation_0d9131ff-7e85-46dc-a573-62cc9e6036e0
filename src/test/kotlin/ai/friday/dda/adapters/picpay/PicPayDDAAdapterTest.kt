package ai.friday.dda.adapters.picpay

import ai.friday.dda.FileUtils
import ai.friday.dda.ServerError
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.Document
import ai.friday.dda.app.UnexpectedResponse
import ai.friday.dda.app.interfaces.DDARequestFilter
import ai.friday.dda.app.interfaces.Filters
import ai.friday.dda.app.metrics.metricRegister
import ai.friday.dda.mapper
import io.kotest.assertions.arrow.core.shouldBeLeft
import io.kotest.assertions.arrow.core.shouldBeRight
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeSameInstanceAs
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.netty.DefaultHttpClient
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import java.time.LocalDate
import utils.DomainConstants
import utils.DomainConstants.Companion.DOCUMENT_2
import utils.DomainFixture.Companion.ITAU
import utils.PicPayDDAFixture
import utils.PicPayDDAFixture.Companion.BANK_SLIP

internal class PicPayDDAAdapterTest : FunSpec({

    val blockingHttpClient = mockk<BlockingHttpClient>()
    val httpClient = mockk<DefaultHttpClient> {
        every { toBlocking() } returns blockingHttpClient
    }

    val converter = mockk<PicPayDDAAdapterConverter> {
        val mockBillRegisterData = mockk<BillRegisterData> {
            every { payerDocument } returns DomainConstants.DOCUMENT
        }

        every { toDDARegister(any()) } returns mockBillRegisterData
    }
    val adapter = PicPayDDAAdapter(httpClient, converter)
    val cip = DDARequestFilter.CIPFilter("cip_id")

    context("when deserialize a response") {
        test("of a bankslip by barcode, should return a BankSlip instance") {
            val json = FileUtils.readLocalFileAsText("picpay-dda/expected_bankslip_by_id_response.json")
            val result = mapper.readValue(json, BankSlipWrapper::class.java)

            val bankSlip = result
                .data.shouldNotBeNull()
                .bankSlip.shouldNotBeNull()

            bankSlip.situation shouldBe BankSlipSituation.OPEN
            bankSlip.bankSlipSituation shouldBe JDBankSlipSituation.OPEN

            bankSlip.identificationNumber.shouldBe("2021010800048576475")
            bankSlip.amount.toFloat().shouldBe(100)
            bankSlip.documentNumber.shouldBe(DOCUMENT_2)
            bankSlip.finalBeneficiary.shouldNotBeNull().document.shouldBe(DOCUMENT_2)
            bankSlip.originalBeneficiary.shouldNotBeNull().document.shouldBe(DOCUMENT_2)
            bankSlip.guarantorDrawer.shouldNotBeNull().document.shouldBe(DOCUMENT_2)
            bankSlip.payer.shouldNotBeNull().document.shouldBe(DOCUMENT_2)

            bankSlip.barcodeNumber.shouldBe("28094839400002000002807664664453548654453693")
            bankSlip.recipientParticipantISPB.shouldBe(ITAU.ispb)
            bankSlip.recipientParticipantCode.shouldNotBeNull().toLongOrNull().shouldBe(ITAU.compe)

            bankSlip.rebateAmount.shouldNotBeNull().toFloat().shouldBe(10)
            bankSlip.discounts.shouldNotBeNull().size.shouldBe(1)

            bankSlip.interest.shouldNotBeNull()
                .percentage.shouldNotBeNull()
                .toFloat().shouldBe(5.55f)

            bankSlip.fine.shouldNotBeNull()
                .amount.shouldNotBeNull()
                .toFloat().shouldBe(12.50f)

            bankSlip.writeOffs.shouldNotBeNull().also {
                it.first().situation.shouldNotBeNull().shouldBe(OperationalWriteOffRequestSituation.PENDING)
                it.last().situation.shouldNotBeNull().shouldBe(OperationalWriteOffRequestSituation.EFFECTIVE)
            }
        }

        test("of a bankslip list operation, should return a BankSlipList instance") {
            val json = FileUtils.readLocalFileAsText("picpay-dda/expected_bankslip_by_filter_response.json")
            val result = mapper.readValue(json, BankSlipListWrapper::class.java)
            val bankSlips = result
                .data.shouldNotBeNull()
                .bankSlips.shouldNotBeNull()

            bankSlips.totalItems.shouldBe(6)
            bankSlips.items.size.shouldBe(6)
            bankSlips.items.first().identificationNumber.shouldBe("2022072905873824970")
            bankSlips.items.last().identificationNumber.shouldBe("2020112600881139414")
        }

        test("of a bankslip by barcode with invalid bankSlipSituation value, should handle it as null and return the BankSlip instance") {

            mockkStatic(::metricRegister)
            every { metricRegister(any(), any()) } just Runs

            val json = FileUtils
                .readLocalFileAsText("picpay-dda/expected_bankslip_by_id_response.json")!!
                .replace(""""bankSlipSituation" : "OPEN"""", """"bankSlipSituation" : "INVALID_VALUE"""")

            mapper.readValue(json, BankSlipWrapper::class.java)
                .data.shouldNotBeNull()
                .bankSlip.shouldNotBeNull().run {
                    bankSlipSituation.shouldBeNull() // ignore invalid value

                    identificationNumber.shouldNotBeNull()
                    barcodeNumber.shouldNotBeNull()
                    typeableBarcodeNumber.shouldNotBeNull()
                }

            verify(exactly = 1) { metricRegister(any<InvalidEnumValue>(), any(), any()) }
        }
    }

    context("when querying a specific bankSlip and") {
        test("request result is ok, should return success with the bankSlip's details") {
            every {
                blockingHttpClient.exchange(
                    any<HttpRequest<String>>(),
                    Argument.of(BankSlipWrapper::class.java),
                    Argument.STRING
                )
            } returns HttpResponse.ok(BankSlipWrapper(BankSlipContainer(BANK_SLIP)))

            val result = adapter.findByCipId(cip).shouldBeRight()
            result.payerDocument shouldBe DomainConstants.DOCUMENT
        }

        test("identification number is not found, should return UnexpectedResponse") {
            val bankSlipNotFound = mockk<BankSlipWrapper>(relaxed = true) {
                every { data } returns null
            }

            every {
                blockingHttpClient.exchange(
                    any<HttpRequest<String>>(),
                    Argument.of(BankSlipWrapper::class.java),
                    Argument.STRING
                )
            } returns HttpResponse.ok(bankSlipNotFound)

            val result = adapter.findByCipId(cip).shouldBeLeft()

            result.shouldBeInstanceOf<UnexpectedResponse>()
        }

        test("request result is an unexpected error, should return unknown error and the raw response") {
            val responseEx = HttpClientResponseException("ERROR", HttpResponse.badRequest("TESTING ERROR"))

            every {
                blockingHttpClient.exchange(
                    any<HttpRequest<String>>(),
                    Argument.of(BankSlipWrapper::class.java),
                    Argument.STRING
                )
            } throws responseEx

            val result = adapter.findByCipId(cip).shouldBeLeft()
            result.shouldBeInstanceOf<ServerError>()
            result.ex shouldBeSameInstanceAs responseEx
        }
    }

    context("when querying a list of bankSlips using a set of filters and") {
        test("request result is ok, should return success with all bankSlips") {
            val bankSlip = BANK_SLIP.copy(identificationNumber = "id-1")
            val bankSlip2 = BANK_SLIP.copy(identificationNumber = "id-2")

            every {
                blockingHttpClient.exchange(
                    any<HttpRequest<String>>(),
                    Argument.of(BankSlipListWrapper::class.java),
                    Argument.STRING
                )
            } returns HttpResponse.ok(
                BankSlipListWrapper(
                    BankSlipListContainer(
                        BankSlipList(2, listOf(bankSlip, bankSlip2))
                    )
                )
            )

            val filter = DDARequestFilter.RangeFilter(
                dateRange = Filters.DateRange(
                    LocalDate.parse(PicPayDDAFixture.DUE_DATE_START),
                    LocalDate.parse(PicPayDDAFixture.DUE_DATE_END)
                ),
                document = Document(DomainConstants.DOCUMENT)
            )

            val result = adapter.findByFilter(filter).shouldBeRight()
            result.shouldNotBeNull()

            result.totalItems shouldBe 2
            result.items.size shouldBe 2
        }

        test("request result is an unexpected error, should return unknown error and the raw response") {
            val responseEx = HttpClientResponseException("ERROR", HttpResponse.badRequest("TESTING ERROR"))

            every {
                blockingHttpClient.exchange(
                    any<HttpRequest<String>>(),
                    Argument.of(BankSlipListWrapper::class.java),
                    Argument.STRING
                )
            } throws responseEx

            val filter = DDARequestFilter.RangeFilter(
                dateRange = Filters.DateRange(
                    LocalDate.parse(PicPayDDAFixture.DUE_DATE_START),
                    LocalDate.parse(PicPayDDAFixture.DUE_DATE_END)
                ),
                document = Document(DomainConstants.DOCUMENT)
            )

            val result = adapter.findByFilter(filter).shouldBeLeft()
            result.shouldBeInstanceOf<ServerError>()
            result.ex shouldBeSameInstanceAs responseEx
        }

        test("has not been found, should return a empty list") {
            every {
                blockingHttpClient.exchange(
                    any<HttpRequest<String>>(),
                    Argument.of(BankSlipListWrapper::class.java),
                    Argument.STRING
                )
            } returns HttpResponse.ok(
                BankSlipListWrapper(
                    BankSlipListContainer(
                        BankSlipList(0, listOf())
                    )
                )
            )

            val filter = DDARequestFilter.RangeFilter(
                dateRange = Filters.DateRange(
                    LocalDate.parse(PicPayDDAFixture.DUE_DATE_START),
                    LocalDate.parse(PicPayDDAFixture.DUE_DATE_END)
                ),
                document = Document(DomainConstants.DOCUMENT_2)

            )

            val result = adapter.findByFilter(filter).shouldNotBeNull().shouldBeRight()
            with(result) {
                totalItems shouldBe 0
                items shouldBe listOf()
            }
        }
    }
})