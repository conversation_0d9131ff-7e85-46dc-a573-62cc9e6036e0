package ai.friday.dda.adapters.picpay

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.http.client.netty.DefaultHttpClient
import io.mockk.every
import io.mockk.mockk

internal class PicPayRegisterAdapterTest : FunSpec({
    val blockingHttpClient = mockk<BlockingHttpClient>()
    val httpClient = mockk<DefaultHttpClient> {
        every { toBlocking() } returns blockingHttpClient
    }
    val picPayRegisterAdapter = PicPayRegisterAdapter(httpClient = httpClient)

    context(name = "sendOptOutNotification") {
        listOf(
            Triple("deve retornar sucesso caso consiga enviar a notificação de opt-out", HttpResponse.ok(), OptOutResult.Success),
            Triple("deve retornar falha caso não consiga enviar a notificação de opt-out", HttpResponse.serverError(), OptOutResult.Failure),
            Triple(
                "deve retornar falha caso o usuário já possua o DDA desativado",
                HttpResponse.unprocessableEntity<OptOutResponse>()
                    .body(OptOutResponse(success = false, code = 422, message = "Usuário já possui DDA desativado")),
                OptOutResult.AccountAlreadyProcessed
            ),
            Triple(
                "deve retornar falha caso o usuário já possua o DDA desativado",
                HttpResponse.unprocessableEntity<OptOutResponse>()
                    .body(OptOutResponse(success = false, code = 422, message = "Usuário não encontrado")),
                OptOutResult.AccountNotFound
            ),
            Triple(
                "deve retornar falha caso o usuário já possua o DDA desativado",
                HttpResponse.unprocessableEntity<OptOutResponse>()
                    .body(OptOutResponse(success = false, code = 422, message = "")),
                OptOutResult.UnprocessableEntity
            ),
            Triple(
                "deve retornar falha caso tenha algum erro na requisição",
                HttpResponse.badRequest<OptOutResponse>()
                    .body(OptOutResponse(success = false, code = 400, message = "")),
                OptOutResult.BadRequest
            )
        ).forEach {
            test(it.first) {
                every {
                    blockingHttpClient.exchange(
                        any<HttpRequest<String>>(),
                        OptOutResponse::class.java
                    )
                } returns it.second

                val result = picPayRegisterAdapter.sendOptOutNotification("123232103")

                result shouldBe it.third
            }
        }
    }
})