package ai.friday.dda.adapters.picpay.messaging

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.assertThrows

class WorkersCalculatorTest : FunSpec({

    listOf(
        Pair(Scale(min = 1, max = 10), Scale(min = 1, max = 10)),
        Pair(Scale(min = 1, max = 10), Scale(min = 1, max = 10)),
        Pair(Scale(min = 1, max = 10), Scale(min = 1, max = 10))
    ).forEach {
        test("should calculate limit constraints appropriately") {

            val input = it.first
            val expected = it.second
            val workersCalculator = WorkersCalculator(input.min, input.max)
            workersCalculator.minWorkers.shouldBe(expected.min)
            workersCalculator.maxWorkers.shouldBe(expected.max)
        }
    }

    listOf(
        Scale(min = -1, max = 9),
        Scale(min = 1, max = -9),
        Scale(min = 10, max = 5)
    ).forEach {
        test("should throw exception if constraints are invalid ones") {
            assertThrows<IllegalStateException> { WorkersCalculator(it.min, it.max) }
        }
    }

    listOf(
        SimpleStatistics(listOf(1000, 10000, 15000, 11000, 5000), true, 2),
        SimpleStatistics(listOf(1000, 10000, 15000, 11000, 5000), false, 3),
        SimpleStatistics(listOf(1000, 3000, 1500, 1000, 4000), false, 5)
    ).forEach {
        test("should scale workers") {
            val workersCalculator =
                WorkersCalculator(
                    minParallelism = 2,
                    maxParallelism = 8,
                    healthIndicatorTimeInMillis = 5000,
                    hardDownScale = it.hardDownScale
                )

            workersCalculator.currentWorkers().shouldBe(2)

            // increment
            repeat(2) {
                for (i in 1..10) {
                    workersCalculator.computeJobElapsedTimeInMillis(1000)
                }
                workersCalculator.currentWorkers()
            }

            workersCalculator.currentWorkers().shouldBe(4)

            it.computedTimes.forEach {
                workersCalculator.computeJobElapsedTimeInMillis(it)
                workersCalculator.computeJobElapsedTimeInMillis(it)
            }

            // should decrement
            workersCalculator.currentWorkers().shouldBe(it.expectedWorkers)
        }
    }

    listOf(
        SimpleStatisticsWithoutScale(
            listOf(1000, 10000, 15000, 11000, 5000),
            Scale(min = 1, max = 10),
            1
        ), // hard limits
        SimpleStatisticsWithoutScale(
            listOf(1000, 3000, 1500, 1000, 4000),
            Scale(min = 10, max = 10),
            10
        ), // hard limits
        SimpleStatisticsWithoutScale(
            listOf(1000, 10000, 15000, 11000, 5000),
            Scale(min = 5, max = 5),
            5
        ), // soft limits
        SimpleStatisticsWithoutScale(listOf(1000, 3000, 1500, 1000, 4000), Scale(min = 5, max = 5), 5) // soft limits
    ).forEach {
        test("should not scale workers") {
            val workersCalculator = WorkersCalculator(it.scale.min, it.scale.max, healthIndicatorTimeInMillis = 5000)

            workersCalculator.currentWorkers().shouldBe(it.scale.min)

            it.computedTimes.forEach {
                workersCalculator.computeJobElapsedTimeInMillis(it)
                workersCalculator.computeJobElapsedTimeInMillis(it)
            }

            workersCalculator.currentWorkers().shouldBe(it.expectedWorkers)
        }
    }
})

private data class Scale(val min: Int, val max: Int)

private data class SimpleStatistics(val computedTimes: List<Long>, val hardDownScale: Boolean, val expectedWorkers: Int)

private data class SimpleStatisticsWithoutScale(
    val computedTimes: List<Long>,
    val scale: Scale,
    val expectedWorkers: Int
)