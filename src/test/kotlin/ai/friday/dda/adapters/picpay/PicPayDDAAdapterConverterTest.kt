package ai.friday.dda.adapters.picpay

import ai.friday.dda.app.AmountCalculationModel
import ai.friday.dda.app.BillStatus
import ai.friday.dda.app.BillType
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.dda.app.DateUtils
import ai.friday.dda.app.DiscountType
import ai.friday.dda.app.FichaCompensacaoType
import ai.friday.dda.app.FineType
import ai.friday.dda.app.InterestType
import ai.friday.dda.app.Recipient
import ai.friday.dda.app.RecipientChain
import ai.friday.dda.app.banking.FinancialInstitution
import ai.friday.dda.app.interfaces.FinancialIdentifier
import ai.friday.dda.app.interfaces.FinancialInstitutionService
import arrow.core.Either
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import utils.DomainConstants.Companion.DOCUMENT
import utils.DomainConstants.Companion.DOCUMENT_2
import utils.PicPayDDAFixture
import utils.PicPayDDAFixture.Companion.BANK_SLIP
import utils.PicPayDDAFixture.Companion.BANK_SLIP_WRITE_OFF
import utils.PicPayDDAFixture.Companion.BANK_SLIP_WRITE_OFF_DETAIL

internal class PicPayDDAAdapterConverterTest : FunSpec({

    val converter = PicPayDDAAdapterConverter(PicPayDDAFixture.institutionService)

    context("should guarantee body conversion to BillRegisterData") {
        test("when billType is CONCESSIONARIA") {
            val convertedData = converter.toDDARegister(BANK_SLIP)

            val sacadorAvalista = Recipient(name = "Galvão", document = DOCUMENT_2)
            val originalBeneficiary = Recipient(name = "Galvão Beneficiary", document = "***********")
            val finalBeneficiary = Recipient(name = "Galvão Final", document = "***********")

            convertedData.amount shouldBe 10050
            convertedData.discount shouldBe 202
            convertedData.interest shouldBe 1099
            convertedData.fine shouldBe 199
            convertedData.amountTotal shouldBe 1096
            convertedData.billType shouldBe BillType.CONCESSIONARIA // TODO: criar um teste para cada tipo
            convertedData.assignor shouldBe "BCO BRADESCO S.A."
            convertedData.recipient?.document shouldBe sacadorAvalista.document
            convertedData.recipient?.name shouldBe sacadorAvalista.name
            convertedData.recipientChain shouldBe RecipientChain(
                sacadorAvalista = sacadorAvalista,
                originalBeneficiary = originalBeneficiary,
                finalBeneficiary = finalBeneficiary
            )
            convertedData.payerDocument shouldBe DOCUMENT
            convertedData.expirationDate shouldBe LocalDate.parse(PicPayDDAFixture.EXPIRATION_DATE)
            convertedData.dueDate shouldBe LocalDate.parse(PicPayDDAFixture.DUE_DATE)
            convertedData.paymentLimitTime shouldBe "20:00" // FIXME: ver se vem esse dado da resposta do PicPay pois está fixo no billpayment
            convertedData.settleDate shouldBe BrazilZonedDateTimeSupplier.getLocalDate()
            convertedData.fichaCompensacaoType shouldBe FichaCompensacaoType.CH_CHEQUE
            convertedData.payerName shouldBe "Globo"
            convertedData.amountCalculationModel shouldBe AmountCalculationModel.ANYONE
            convertedData.interestData?.type shouldBe InterestType.VALUE
            convertedData.interestData?.value shouldBe BigDecimal.valueOf(0.04)
            convertedData.interestData?.date shouldBe LocalDate.parse(PicPayDDAFixture.INTEREST_DATE)

            convertedData.fineData?.type shouldBe FineType.PERCENT
            convertedData.fineData?.value shouldBe BigDecimal.valueOf(15)
            convertedData.fineData?.date shouldBe LocalDate.parse(PicPayDDAFixture.FINE_DATE)

            convertedData.discountData?.type shouldBe DiscountType.FIXED_UNTIL_DATE
            convertedData.discountData?.value1 shouldBe BANK_SLIP.discounts?.get(0)?.amount
            convertedData.discountData?.date1 shouldBe LocalDate.parse(BANK_SLIP.discounts?.get(0)?.moment.toString())
            convertedData.discountData?.value2.shouldBeNull()
            convertedData.discountData?.date2.shouldBeNull()
            convertedData.discountData?.value3.shouldBeNull()
            convertedData.discountData?.date3.shouldBeNull()

            convertedData.discounts?.size shouldBe 1
            convertedData.discounts?.first()?.type shouldBe DiscountType.FIXED_UNTIL_DATE
            convertedData.discounts?.first()?.value shouldBe 1500L
            convertedData.discounts?.first()?.date shouldBe LocalDate.parse(BANK_SLIP.discounts?.get(0)?.moment.toString())

            convertedData.abatement shouldBe 100.5
            convertedData.rebate shouldBe 10050
            convertedData.amountPaid shouldBe 9000
            convertedData.paidDate.shouldNotBeNull()
            convertedData.idNumber shouldBe "f0caa841-18fc-4585-8797-5c9b0495a386"
            convertedData.barCode.shouldNotBeNull().number.shouldBe("836800000009857900531073096493867111101007954817")
            convertedData.barCode.shouldNotBeNull().digitable.shouldBe("836800000009985790053104730964938676111101007958")
            convertedData.billStatus shouldBe BillStatus.BAIXA_EFETIVA_REALIZADA
        }
    }

    context("should convert bankSlip recipient appropriately") {

        val institutionService = mockk<FinancialInstitutionService>() {
            every { getInstitutionByCode(FinancialIdentifier.COMPE(0)) } returns Either.Right(null)

            every { getInstitutionByCode(FinancialIdentifier.COMPE(341)) } returns FinancialInstitution(
                name = "ITAU UNIBANCO S.A.",
                "********",
                341
            ).right()
        }

        val localConverter = PicPayDDAAdapterConverter(institutionService)

        val bankSlipPayerEverywhere = BANK_SLIP.copy(
            recipientParticipantCode = "341",
            payer = BANK_SLIP.payer.copy(document = DOCUMENT),
            guarantorDrawer = BANK_SLIP.guarantorDrawer?.copy(document = DOCUMENT),
            finalBeneficiary = BANK_SLIP.finalBeneficiary?.copy(document = DOCUMENT),
            originalBeneficiary = BANK_SLIP.originalBeneficiary?.copy(document = DOCUMENT)
        )

        val bankSlipDifferentGuarantorDrawer = bankSlipPayerEverywhere.copy(
            guarantorDrawer = BANK_SLIP.guarantorDrawer?.copy(document = DOCUMENT_2)
        )

        val bankSlipDifferentFinalBeneficiary = bankSlipPayerEverywhere.copy(
            finalBeneficiary = BANK_SLIP.finalBeneficiary?.copy(document = DOCUMENT_2)
        )

        val bankSlipDifferentOriginalBeneficiary = bankSlipPayerEverywhere.copy(
            originalBeneficiary = BANK_SLIP.originalBeneficiary?.copy(document = DOCUMENT_2)
        )

        val bankSlipWithGuarantorDrawerDocumentNull =
            bankSlipPayerEverywhere.copy(
                guarantorDrawer = bankSlipPayerEverywhere.guarantorDrawer?.copy(document = null),
                finalBeneficiary = BANK_SLIP.finalBeneficiary?.copy(document = DOCUMENT_2)
            )

        listOf(
            Triple("when guarantorDrawer isn't the payer", bankSlipDifferentGuarantorDrawer, DOCUMENT_2),
            Triple("when final beneficiary isn't the payer", bankSlipDifferentFinalBeneficiary, DOCUMENT_2),
            Triple("when original beneficiary isn't the payer", bankSlipDifferentOriginalBeneficiary, DOCUMENT_2),
            Triple(
                "when guarantorDrawer document is null and final beneficiary isn't the payer",
                bankSlipWithGuarantorDrawerDocumentNull,
                DOCUMENT_2
            )
        ).forEach {
            test(it.first) {
                localConverter.toDDARegister(it.second).recipient?.document shouldBe it.third
            }
        }

        test("when original and final beneficiaries and guarantorDrawer are the payer, should return the assignor") {
            val result = localConverter.toDDARegister(bankSlipPayerEverywhere.copy(recipientParticipantCode = "341"))
            result.recipient.shouldNotBeNull().name.shouldBe("ITAU UNIBANCO S.A.")
        }

        test("when assignor is the recipient but we don't have the register of the bank should return recipient ispb") {
            val result = localConverter.toDDARegister(
                bankSlipPayerEverywhere.copy(
                    recipientParticipantCode = "000",
                    recipientParticipantISPB = "********"
                )
            )
            result.recipient.shouldNotBeNull().name.shouldBe("********")
            result.assignor.shouldBe("********")
        }

        test("when recipientParticipantCode is not a valid number") {
            val result = localConverter.toDDARegister(
                bankSlipPayerEverywhere.copy(
                    recipientParticipantCode = "SEM",
                    recipientParticipantISPB = "********"
                )
            )
            result.recipient.shouldNotBeNull().name.shouldBe("********")
        }
    }

    context("when calculate amount paid") {
        context("for a credit card bill") {
            test("should ignore the write offs older than dueDate minus 12 days") {
                val bankSlip = converter.toDDARegister(
                    BANK_SLIP.copy(
                        speciesCode = BankSlipSpeciesCode.CREDIT_CARD,
                        expirationDate = LocalDate.now(), // dueDate
                        writeOffs = listOf(
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "1",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal.TEN,
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(16) // should ignore
                                    )
                                )
                            ),
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "2",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(15),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(12) // should consider
                                    )
                                )
                            ),
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "3",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal.ONE,
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(1) // should consider
                                    )
                                )
                            )
                        )
                    )
                )

                bankSlip.amountPaid.shouldNotBeNull().shouldBe(1600) // 16
                bankSlip.paidDate.shouldNotBeNull().shouldBe(LocalDate.now().minusDays(1))
            }
        }

        context("for a non credit card bill") {
            test("should accept all available write offs") {
                val bankSlip = converter.toDDARegister(
                    BANK_SLIP.copy(
                        speciesCode = BankSlipSpeciesCode.OTHERS,
                        expirationDate = LocalDate.now(), // dueDate
                        writeOffs = listOf(
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "1",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal.TEN,
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(16) // should consider
                                    )
                                )
                            ),
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "2",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(12),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(12) // should consider
                                    )
                                )
                            ),
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "3",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(18),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(1) // should consider
                                    )
                                )
                            )
                        )
                    )
                )

                bankSlip.amountPaid.shouldNotBeNull().shouldBe(4000) // 40
                bankSlip.paidDate.shouldNotBeNull().shouldBe(LocalDate.now().minusDays(1))
            }

            test("should deduplicate write offs by their identification numbers") {
                val bankSlip = converter.toDDARegister(
                    BANK_SLIP.copy(
                        speciesCode = BankSlipSpeciesCode.OTHERS,
                        expirationDate = LocalDate.now(),
                        writeOffs = listOf(
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "1",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(3),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(1) // should consider
                                    )
                                )
                            ),
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "2",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(5),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(1) // should consider
                                    )
                                )
                            ),
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "3",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(8),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(1) // should consider
                                    )
                                )
                            )
                        )
                    )
                )

                bankSlip.amountPaid.shouldNotBeNull().shouldBe(1600)
                bankSlip.paidDate.shouldNotBeNull().shouldBe(LocalDate.now().minusDays(1))
            }
        }

        test("should return zero if there are not write offs") {
            val bankSlip = converter.toDDARegister(
                BANK_SLIP.copy(writeOffs = listOf())
            )

            bankSlip.amountPaid.shouldNotBeNull().shouldBe(0)
            bankSlip.paidDate.shouldBeNull()
        }

        test("should ignore write offs with REJECTED situation") {
            val bankSlip = converter.toDDARegister(
                BANK_SLIP.copy(
                    writeOffs = listOf(
                        BANK_SLIP_WRITE_OFF.copy(
                            details = listOf(
                                BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                    writeOffDetailBankSlipAmount = BigDecimal(100),
                                    bankSlipReceiveAt = LocalDateTime.now().minusDays(7)
                                )
                            ),
                            situation = OperationalWriteOffRequestSituation.SENT // should consider
                        ),
                        BANK_SLIP_WRITE_OFF.copy(
                            details = listOf(
                                BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                    writeOffDetailBankSlipAmount = BigDecimal(200),
                                    bankSlipReceiveAt = LocalDateTime.now().minusDays(7)
                                )
                            ),
                            situation = OperationalWriteOffRequestSituation.PENDING // should consider
                        ),
                        BANK_SLIP_WRITE_OFF.copy(
                            details = listOf(
                                BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                    writeOffDetailBankSlipAmount = BigDecimal(300),
                                    bankSlipReceiveAt = LocalDateTime.now().minusDays(7)
                                )
                            ),
                            situation = OperationalWriteOffRequestSituation.EFFECTIVE // should consider
                        ),
                        BANK_SLIP_WRITE_OFF.copy(
                            details = listOf(
                                BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                    writeOffDetailBankSlipAmount = BigDecimal(10000),
                                    bankSlipReceiveAt = LocalDateTime.now().minusDays(7)
                                )
                            ),
                            situation = OperationalWriteOffRequestSituation.REJECTED // should ignore
                        )
                    )
                )
            )

            bankSlip.amountPaid.shouldNotBeNull().shouldBe(60000)
            bankSlip.paidDate.shouldNotBeNull().shouldBe(LocalDate.now().minusDays(7))
        }

        test("should ignore write offs with empty details") {
            val bankSlip = converter.toDDARegister(
                BANK_SLIP.copy(
                    writeOffs = listOf(
                        BANK_SLIP_WRITE_OFF.copy(
                            identificationNumber = "123",
                            details = listOf()
                        ),
                        BANK_SLIP_WRITE_OFF.copy(
                            identificationNumber = "345",
                            details = listOf()
                        )
                    )
                )
            )

            bankSlip.amountPaid.shouldNotBeNull().shouldBe(0)
            bankSlip.paidDate.shouldBeNull()
        }

        context("should return the sum of recent effective and operational write offs") {
            test("if they are all different") {
                val bankSlip = converter.toDDARegister(
                    BANK_SLIP.copy(
                        writeOffs = listOf(
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "1", // should ignore
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(150),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(7)
                                    )
                                )
                            ),
                            BANK_SLIP_WRITE_OFF.copy(
                                identificationNumber = "3",
                                details = listOf(
                                    BANK_SLIP_WRITE_OFF_DETAIL.copy(
                                        writeOffDetailBankSlipAmount = BigDecimal(50),
                                        bankSlipReceiveAt = LocalDateTime.now().minusDays(1) // should consider
                                    )
                                )
                            )
                        )
                    )
                )

                bankSlip.amountPaid.shouldNotBeNull().shouldBe(20000)
                bankSlip.paidDate.shouldNotBeNull().shouldBe(LocalDate.now().minusDays(1))
            }
        }
    }

    context("when there are multiple calculations") {

        context("and all calculations doesnt have dueDate") {

            val calculation1 = PicPayDDAFixture.buildCalculation(totalAmount = 1000)
            val calculation2 = PicPayDDAFixture.buildCalculation(totalAmount = 2000)
            val calculation3 = PicPayDDAFixture.buildCalculation(totalAmount = 3000)

            val bankSlip = BANK_SLIP.copy(
                calculations = listOf(calculation1, calculation2, calculation3)
            )

            test("should return the first calculation") {
                bankSlip.getCurrentCalculation() shouldBe calculation1
            }
        }

        context("and all calculations have dueDate") {

            val calculation1 = PicPayDDAFixture.buildCalculation(
                totalAmount = 1000,
                dueDate = getLocalDate().minusDays(1)
            )
            val calculation2 = PicPayDDAFixture.buildCalculation(
                totalAmount = 2000,
                dueDate = getLocalDate()
            )
            val calculation3 = PicPayDDAFixture.buildCalculation(
                totalAmount = 3000,
                dueDate = getLocalDate().plusDays(1)
            )

            val bankSlip = BANK_SLIP.copy(
                calculations = listOf(calculation1, calculation2, calculation3)
            )

            test("should return the calculation with the nearest due date") {
                bankSlip.getCurrentCalculation() shouldBe calculation2
            }
        }

        context("and all calculations with dueDate are overdue but there is a calculation without dueDate") {

            val calculation1 =
                PicPayDDAFixture.buildCalculation(totalAmount = 1000, dueDate = LocalDate.now().minusDays(10))
            val calculation2 =
                PicPayDDAFixture.buildCalculation(totalAmount = 2000, dueDate = LocalDate.now().minusDays(5))
            val calculation3 =
                PicPayDDAFixture.buildCalculation(totalAmount = 3000)

            val bankSlip = BANK_SLIP.copy(
                calculations = listOf(calculation1, calculation2, calculation3)
            )

            test("should return the calculation without dueDate") {
                bankSlip.getCurrentCalculation() shouldBe calculation3
            }
        }
    }

    context("when calculate the round trip time of a bankslip") {
        val zonedDateTimeNow = DateUtils.now()
        val localDateTimeNow = zonedDateTimeNow.toLocalDateTime()

        test("and the most recent date is in the past, should not return this one") {
            val yesterday = localDateTimeNow.toLocalDate().minusDays(1)

            val bankslip = BANK_SLIP.copy(
                dispatchDate = yesterday,
                writeOffs = emptyList(),
                updatedAt = yesterday.atStartOfDay(),
            )

            bankslip.rtt().shouldBeNull()
        }

        test("and the most recent date is in the bankslip root, should return this one") {
            val bankslip = BANK_SLIP.copy(
                dispatchDate = localDateTimeNow.toLocalDate(),
                writeOffs = emptyList(),
                updatedAt = localDateTimeNow.minusSeconds(90)
            )

            bankslip.rtt().shouldNotBeNull().shouldBe(90)
        }
        test("and the most recent date is in the bankslip write offs, should return this one") {
            val bankslip = BANK_SLIP.copy(
                writeOffs = listOf(
                    BANK_SLIP_WRITE_OFF.copy(
                        updatedAt = localDateTimeNow.minusSeconds(500)
                    ),
                    BANK_SLIP_WRITE_OFF.copy(
                        updatedAt = localDateTimeNow.minusSeconds(90)
                    ),
                    BANK_SLIP_WRITE_OFF.copy(
                        updatedAt = localDateTimeNow.minusSeconds(900)
                    )
                ),
                updatedAt = localDateTimeNow.minusDays(5)
            )

            bankslip.rtt().shouldNotBeNull().shouldBe(90)
        }
    }

    context("when calculate bill status") {
        test("should prioritize jdSituation over situation only if it is not null") {
            listOf(
                null to BANK_SLIP.copy(situation = null, bankSlipSituation = null),
                BillStatus.ABERTO to BANK_SLIP.copy(situation = BankSlipSituation.OPEN, bankSlipSituation = null),
                BillStatus.ABERTO to BANK_SLIP.copy(situation = null, bankSlipSituation = JDBankSlipSituation.OPEN),
                BillStatus.BAIXA_EFETIVA_REALIZADA to BANK_SLIP.copy(
                    situation = BankSlipSituation.OPEN, // must be ignored
                    bankSlipSituation = JDBankSlipSituation.WRITE_OFF_BY_INTRA_BANK_SETTLEMENT
                ),
            ).forEach { testCase ->
                testCase.let { (expectedBillStatus, bankSlip) ->
                    converter.toDDARegister(bankSlip).billStatus shouldBe expectedBillStatus
                }
            }
        }
    }
})