package ai.friday.dda.adapters.picpay

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldNotContain
import java.time.LocalDate
import utils.DomainConstants
import utils.PicPayDDAFixture.Companion.DUE_DATE
import utils.PicPayDDAFixture.Companion.DUE_DATE_END
import utils.PicPayDDAFixture.Companion.DUE_DATE_START
import utils.PicPayDDAFixture.Query.EXPECTED_BANK_SLIP_QUERY
import utils.PicPayDDAFixture.Query.EXPECTED_COMPLETE_BANK_SLIP_FILTERED_QUERY
import utils.PicPayDDAFixture.Query.EXPECTED_PARTIAL_BANK_SLIP_FILTERED_QUERY

internal class PicPayQueryFactoryTest : FunSpec({
    test("should create graphql bankSlip query appropriately") {
        PicPayQueryFactory.queryBankSlip("cip_id") shouldBe EXPECTED_BANK_SLIP_QUERY
    }

    test("should create graphql bankSlipList query with all arguments appropriately") {
        PicPayQueryFactory.queryBankSlipList(
            BankSlipsFilter(
                payer = PersonFilter(PersonType.NATURAL, DomainConstants.DOCUMENT),
                type = BankSlipTypeFilter.OWN,
                range = BankSlipRange(50, 100),
                dueDate = DateFilter(LocalDate.parse(DUE_DATE), LocalDate.parse(DUE_DATE_END)),
                registration = DateFilter(
                    LocalDate.parse(DUE_DATE_START),
                    LocalDate.parse(DUE_DATE_END)
                ),
                situation = BankSlipSituation.OPEN,
                paymentSituation = PaymentSituation.BANK_SLIP_ALREADY_WRITTEN_OFF
            )
        ) shouldBe EXPECTED_COMPLETE_BANK_SLIP_FILTERED_QUERY
    }

    test("should create graphql bankSlipList query with some arguments appropriately") {
        val query = PicPayQueryFactory.queryBankSlipList(
            BankSlipsFilter(
                payer = PersonFilter(PersonType.NATURAL, DomainConstants.DOCUMENT),
                range = BankSlipRange(50, 100)
            )
        )

        query shouldNotContain DUE_DATE_START
        query shouldNotContain DUE_DATE_END
        query shouldNotContain DUE_DATE
        query shouldNotContain PaymentSituation.BANK_SLIP_ALREADY_WRITTEN_OFF.name
        query shouldNotContain BankSlipSituation.OPEN.name
        query shouldNotContain BankSlipTypeFilter.OWN.name

        query shouldBe EXPECTED_PARTIAL_BANK_SLIP_FILTERED_QUERY
    }

    test("should create graphql query appropriately") {
        PicPayQueryFactory.queryBankSlip("cip_id") shouldBe EXPECTED_BANK_SLIP_QUERY
    }
})