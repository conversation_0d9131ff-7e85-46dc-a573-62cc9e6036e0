package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.ServerError
import ai.friday.dda.app.dda.DDAFullImportService
import ai.friday.dda.app.dda.FullImportResult
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

internal class PicPayFullImportMessageHandlerTest : FunSpec({
    lateinit var service: DDAFullImportService

    lateinit var handler: PicPayFullImportMessageHandler

    beforeEach {
        service = mockk()

        handler =
            PicPayFullImportMessageHandler(mockk(relaxed = true), mockk(relaxed = true), mockk(relaxed = true), service)
    }

    context("para cada mensagem recebida pelo handler") {
        val message = Message.builder().body("""{"document":"document"}""".trimIndent()).attributes(
            mapOf(
                MessageSystemAttributeName.APPROXIMATE_FIRST_RECEIVE_TIMESTAMP to "1111111",
                MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT to "2"
            )
        ).build()

        test("e o full import for realizado com sucesso, deve remover mensagem da fila") {
            every { service.handleFullImport(any()) } returns FullImportResult.Success(10).right()

            handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

            verify {
                service.handleFullImport(
                    withArg {
                        it.document.shouldBe("document")
                        it.createdAtInMillis.shouldBe(1111111)
                        it.attempts.shouldBe(2)
                        it.activateDDA.shouldBeTrue()
                    }
                )
            }
        }

        test("e nao for para ativar o dda, deve solicitar exclusivamente o full import") {
            val onlyImportBillsMessage = Message.builder().body("""{"document":"document","activateDDA":false}""".trimIndent()).attributes(
                mapOf(
                    MessageSystemAttributeName.APPROXIMATE_FIRST_RECEIVE_TIMESTAMP to "1111111",
                    MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT to "2"
                )
            ).build()
            every { service.handleFullImport(any()) } returns FullImportResult.Success(10).right()

            handler.handleMessage(onlyImportBillsMessage).shouldDeleteMessage.shouldBeTrue()

            verify {
                service.handleFullImport(
                    withArg {
                        it.document.shouldBe("document")
                        it.createdAtInMillis.shouldBe(1111111)
                        it.attempts.shouldBe(2)
                        it.activateDDA.shouldBeFalse()
                    }
                )
            }
        }

        test("e o full import for realizado com sucesso sem processar nenhum título, deve manter mensagem da fila") {
            every { service.handleFullImport(any()) } returns FullImportResult.NoContent.right()

            handler.handleMessage(message).shouldDeleteMessage.shouldBeFalse()

            verify {
                service.handleFullImport(any())
            }
        }

        test("e o full import for realizado com erro, devemos manter a mensagem na fila") {
            every { service.handleFullImport(any()) } returns ServerError().left()

            handler.handleMessage(message).shouldDeleteMessage.shouldBeFalse()

            verify {
                service.handleFullImport(
                    withArg {
                        it.document.shouldBe("document")
                        it.createdAtInMillis.shouldBe(1111111)
                        it.attempts.shouldBe(2)
                    }
                )
            }
        }
    }
})