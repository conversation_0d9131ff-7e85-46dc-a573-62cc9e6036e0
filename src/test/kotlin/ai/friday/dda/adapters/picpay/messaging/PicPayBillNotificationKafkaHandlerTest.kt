package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.DDAEventFixture
import ai.friday.dda.ServerError
import ai.friday.dda.adapters.aws.SQSMessagePublisher
import ai.friday.dda.adapters.picpay.PersonType
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.DDA_RECEIVED_EVENT_NAME
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.KAFKA_APPLICATION_NAME
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.Topics.DDA_BANKSLIP_NOTIFICATIONS_STATUS_TOPIC_NAME
import ai.friday.dda.adapters.picpay.messaging.KafkaConstants.Topics.DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME
import ai.friday.dda.app.UnexpectedResponse
import ai.friday.dda.app.brazilTimeZone
import ai.friday.dda.app.dda.DDAEventService
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.convert
import ai.friday.dda.write
import arrow.core.Either
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDateTime
import java.util.Collections
import java.util.concurrent.Executors
import org.apache.kafka.clients.consumer.Consumer
import org.apache.kafka.clients.consumer.OffsetAndMetadata
import org.apache.kafka.common.TopicPartition
import utils.NoStackTraceException
import withGivenDateTime

private const val REQUEST_ID = "external-request-id"

internal class PicPayBillNotificationKafkaHandlerTest : FunSpec({
    val service = mockk<DDAEventService>()

    val sqsPublisher = mockk<SQSMessagePublisher>(relaxed = true)

    val kafkaPublisher = mockk<PicPayKafkaPublisher>(relaxed = true)
    val statusEmitter = PicPayBillNotificationStatusEmitter(kafkaPublisher)

    val configuration = mockk<MessagePublisherConfiguration> {
        every { queueName } returns "my-queue-name"
    }

    val executorService = spyk(Executors.newFixedThreadPool(1))

    val handler = PicPayBillNotificationKafkaHandler(service, sqsPublisher, configuration, executorService, statusEmitter)

    val record = KafkaRecord(
        value = write(DDAEventFixture.createEvent()),
        offset = 1,
        partition = 1,
    )

    context("quando receber um evento de DDA") {

        context("e receber um payload válido") {
            test("deve conseguir parsear o evento") {
                val event = """{"payer": {"type": "LEGAL","document": "***********"},"bankSlip": {"id": "2022091302802930782","updatedAt": "2022-08-13 19:30"}, "id": "friday", "type": "UPDATE"}""".trimIndent()

                val convertedEvent = convert<PicPayDDAEvent>(event)

                with(convertedEvent) {
                    payer.document shouldBe "***********"
                    payer.type shouldBe PersonType.LEGAL
                    bankSlip.id shouldBe "2022091302802930782"
                    bankSlip.updatedAt shouldBe "2022-08-13 19:30"
                    type shouldBe "UPDATE"
                }
            }

            test("realizar a chamada ao servico") {
                test("e sua execução for bem sucedida") {
                    every { service.handleEvent(any(), REQUEST_ID, any()) } returns Unit.right()

                    val currentDateTime = LocalDateTime.of(1989, 4, 9, 9, 20, 0).atZone(brazilTimeZone)

                    withGivenDateTime(currentDateTime) { handler.doProcess(record) }

                    verify {
                        service.handleEvent("cip_id", REQUEST_ID, "NEW")
                        kafkaPublisher.send(
                            DDA_BANKSLIP_NOTIFICATIONS_STATUS_TOPIC_NAME,
                            REQUEST_ID,
                            KAFKA_APPLICATION_NAME,
                            withArg<PicPayDDAReceivedEventTO> {
                                it.body.source.bankslipId shouldBe "cip_id"
                                it.body.source.sentAt shouldBe "1989-04-09T09:20:00.000"
                                it.body.event shouldBe DDA_RECEIVED_EVENT_NAME
                            }
                        )
                    }
                    verify(exactly = 0) {
                        sqsPublisher.sendMessage(any())
                    }
                }

                context("e a consulta finalizar com erro") {
                    listOf(
                        "deve retentar se for um cenário mapeado" to UnexpectedResponse,
                        "deve retentar se for um cenário inesperado" to ServerError(err = NoStackTraceException())
                    ).forEach { (title, error) ->
                        test(title) {
                            every { service.handleEvent(any(), REQUEST_ID, any()) } returns Either.Left(error)

                            handler.doProcess(record)

                            verify {
                                service.handleEvent("cip_id", REQUEST_ID, "NEW")
                                sqsPublisher.sendMessage(
                                    withArg {
                                        it.queueName shouldBe "my-queue-name"
                                        it.jsonObject shouldBe record.value
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }

        test("nao deve emitir evento de status se o emitter estiver desabilitado") {
            clearMocks(kafkaPublisher)
            every { service.handleEvent(any(), REQUEST_ID, any()) } returns Unit.right()

            val handkerWithoutEmitter = PicPayBillNotificationKafkaHandler(service, sqsPublisher, configuration, executorService, null)
            handkerWithoutEmitter.doProcess(record)

            verify {
                service.handleEvent("cip_id", REQUEST_ID, "NEW")
                kafkaPublisher wasNot Called
            }
        }

        test("e não conseguir parsear o evento, deve enviar para a dlq") {
            val eventBody = """{"payerWrong": {"type": "LEGAL","document": "***********"}}""".trimIndent()
            val invalidRecord = KafkaRecord(value = eventBody, offset = 1, partition = 1)

            handler.doProcess(invalidRecord)

            verify {
                sqsPublisher.sendMessage(
                    withArg {
                        it.queueName shouldBe "my-queue-name"
                        it.jsonObject shouldBe invalidRecord.value
                    }
                )
                kafkaPublisher wasNot Called
            }
        }
    }

    test("ao receber eventos de DDA em lote e processar com sucesso deve realizar o commit") {
        val kafkaConsumer = mockk<Consumer<String, String>>(relaxed = true, relaxUnitFun = true)
        every { service.handleEvent(any(), REQUEST_ID, any()) } returns Unit.right()

        handler.onMessage(
            values = listOf(
                write(DDAEventFixture.createEvent()),
                write(DDAEventFixture.createEvent())
            ),
            offsets = listOf(99, 200),
            partitions = listOf(1, 7),
            kafkaConsumer = kafkaConsumer,
        )

        verify {
            kafkaConsumer.commitSync(
                Collections.singletonMap(
                    TopicPartition(DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME, 1),
                    OffsetAndMetadata(100)
                )
            )
            kafkaConsumer.commitSync(
                Collections.singletonMap(
                    TopicPartition(DDA_BANKSLIP_NOTIFICATIONS_TOPIC_NAME, 7),
                    OffsetAndMetadata(201),
                )
            )
        }
    }
})