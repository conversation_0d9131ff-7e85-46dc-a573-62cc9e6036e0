package ai.friday.dda.adapters.picpay.messaging

import ai.friday.dda.DDARegisterFixture
import ai.friday.dda.DynamoDBUtils.putItem
import ai.friday.dda.DynamoDBUtils.setupDynamoDB
import ai.friday.dda.adapters.aws.SQSMessagePublisher
import ai.friday.dda.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.dda.adapters.dynamodb.DDADbRepository
import ai.friday.dda.adapters.dynamodb.DDADynamoDAO
import ai.friday.dda.adapters.dynamodb.toEntity
import ai.friday.dda.app.account.AccountId
import ai.friday.dda.app.dda.DDAProvider
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDARegisterService
import ai.friday.dda.app.dda.DDAStatus
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import ai.friday.dda.write
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.configuration.kafka.exceptions.KafkaListenerException
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.ZonedDateTime
import org.apache.kafka.clients.consumer.ConsumerRecord

class PicPayDDARegisterResponseHandlerTest : FunSpec({

    val dynamoDbEnhancedClient = setupDynamoDB()

    val ddaRepository = DDADbRepository(DDADynamoDAO(dynamoDbEnhancedClient))
    val ddaService = DDARegisterService(
        mockk(relaxed = true),
        ddaRepository,
        mockk {
            every { sendMessage(any(), any(), any()) } returns Unit.right()
        }
    )
    val publisher = mockk<SQSMessagePublisher>(relaxed = true)

    val configuration = mockk<MessagePublisherConfiguration>() {
        every { queueName } returns "my-queue-name"
    }

    val handler = PicPayDDARegisterResponseHandler(ddaService, publisher, configuration)

    val ddaRegister = DDARegister(
        accountId = AccountId("**********"),
        document = "**********",
        created = ZonedDateTime.now(),
        status = DDAStatus.REQUESTING,
        lastUpdated = ZonedDateTime.now(),
        lastSuccessfullExecution = null,
        provider = DDAProvider.PICPAY
    )

    fun save(ddaRegister: DDARegister) {
        putItem(
            client = dynamoDbEnhancedClient,
            tableName = BILL_PAYMENT_TABLE_NAME,
            item = ddaRegister
                .toEntity()
        )
    }

    beforeTest {
        save(ddaRegister)
    }

    // TODO o que fazer se o usuario esta DENIED? - nao faz nada e alarma?
    context("recebe a resposta de uma solicitação de OptIN") {
        context("com um evento de registro aprovado") {
            test("e o usuario não é encontrado") {
                val event = DDARegisterFixture.ddaEventOptInApproved(document = "************")
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event.toJson())
                handler.onMessage(record)

                ddaRepository.findByDocument("************").shouldBeNull()
            }

            DDAStatus.values().filter { it !in DDARegisterService.ApprovableStatuses }.forEach { status ->
                test("e o usuario está com status $status") {
                    save(ddaRegister.copy(status = status))
                    val event = DDARegisterFixture.ddaEventOptInApproved(document = ddaRegister.document)
                    val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event.toJson())
                    handler.onMessage(record)

                    ddaRepository.find(ddaRegister.accountId)?.status shouldBe status
                }
            }

            test("e o usuario está com status PENDING") {
                save(ddaRegister.copy(status = DDAStatus.PENDING))

                val event = DDARegisterFixture.ddaEventOptInApproved(document = ddaRegister.document)
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event.toJson())
                handler.onMessage(record)

                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.REQUESTED
            }

            test("e o usuario deve ser atualizado para status REQUESTED") {
                val event = DDARegisterFixture.ddaEventOptInApproved(document = ddaRegister.document)
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event.toJson())
                handler.onMessage(record)

                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.REQUESTED
            }
        }

        context("com um evento de registro reprovado") {
            test("e o usuario nao é encontrado") {
                val message = DDARegisterFixture.ddaEventOptInReproved(document = "************")
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                handler.onMessage(record)

                ddaRepository.findByDocument("************").shouldBeNull()
            }

            listOf(DDAStatus.PENDING, DDAStatus.REQUESTING, DDAStatus.REQUESTED).forEach { status ->
                test("e o status for $status, o status deve ser atualizado para DENIED") {
                    save(ddaRegister.copy(status = status))
                    val message = DDARegisterFixture.ddaEventOptInReproved(document = ddaRegister.document)
                    val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                    handler.onMessage(record)

                    ddaRepository.findByDocument(ddaRegister.document)?.status shouldBe DDAStatus.DENIED
                }
            }

            DDAStatus.values().filter { it !in DDARegisterService.DenyableStatuses }
                .forEach { status ->
                    test("e o status for $status, o status não deve ser alterado") {
                        save(ddaRegister.copy(status = status))
                        val message = DDARegisterFixture.ddaEventOptInReproved(document = ddaRegister.document)
                        val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                        handler.onMessage(record)

                        ddaRepository.find(ddaRegister.accountId)?.status shouldBe status
                    }
                }
        }

        context("com um evento de registro com erro") {
            test("e o usuario nao é encontrado") {
                val message = DDARegisterFixture.ddaEventOptInError(document = "************")
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                handler.onMessage(record)

                ddaRepository.findByDocument("************").shouldBeNull()
            }

            context("que o DDA já foi registrado") {

                val message = DDARegisterFixture.ddaEventOptInError(
                    document = ddaRegister.document,
                    errorCode = "DDA_ALREADY_EXISTS",
                    errorMessage = "DDA with correlation id 1f520118-3555-4e42-8f44-cd82ccdd27e9 already exists"
                )
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())

                test("deve atualizar o status para REQUESTED") {
                    handler.onMessage(record)
                    ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.REQUESTED
                }
            }

            context("qualquer") {

                val message = DDARegisterFixture.ddaEventOptInError(document = ddaRegister.document)
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())

                test("deve atualizar o status para DENIED") {
                    handler.onMessage(record)
                    ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.DENIED
                }
            }
        }

        context("que seja originado por um pedido de OptOUT") {
            test("com erro de pagador não registrado e inelegível ao OptOUT") {
                save(ddaRegister.copy(status = DDAStatus.CLOSING))
                val message = DDARegisterFixture.ddaEventOptInErrorForOptOutRequest(
                    document = ddaRegister.document,
                    errorCode = "PAYER_NOT_REGISTERED",
                    errorMessage = "Payer a1348a19-984c-4cca-98ec-776a024bf2ec1674767333167 is not eligible for opt-out."
                )

                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                handler.onMessage(record)

                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.CLOSED
            }

            test("com erro informando que situação do pagador permite exclusão") {
                save(ddaRegister.copy(status = DDAStatus.CLOSING))
                val message = DDARegisterFixture.ddaEventOptInErrorForOptOutRequest(
                    document = ddaRegister.document,
                    errorCode = "INTEGRATION_ERROR",
                    errorMessage = "Situação do Pagador Eletrônico não permite Exclusão"
                )

                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                handler.onMessage(record)

                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.CLOSED
            }

            test("com erro genérico") {
                save(ddaRegister.copy(status = DDAStatus.CLOSING))
                val message = DDARegisterFixture.ddaEventOptInErrorForOptOutRequest(
                    document = "************",
                    errorCode = "UNKNOWN_ERROR",
                    errorMessage = "ERRO QUINHENTOS"
                )

                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                handler.onMessage(record)

                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.CLOSING
            }
        }
    }

    context("recebe a resposta de uma solicitação de OptOUT") {

        context("com um evento de registro aprovado") {

            test("e o usuario não é encontrado") {
                val event = DDARegisterFixture.ddaEventOptOutApproved(document = "************")
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event.toJson())
                handler.onMessage(record)

                ddaRepository.findByDocument("************").shouldBeNull()
            }

            DDAStatus.values().filter { it !in DDARegisterService.OptOutValidStatuses }.forEach { status ->
                test("e o usuario está com status $status") {
                    save(ddaRegister.copy(status = status))
                    val event = DDARegisterFixture.ddaEventOptOutApproved(document = ddaRegister.document)
                    val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event.toJson())
                    handler.onMessage(record)

                    ddaRepository.find(ddaRegister.accountId)?.status shouldBe status
                }
            }

            test("e o usuario está com status PENDING_CLOSE") {
                save(ddaRegister.copy(status = DDAStatus.PENDING_CLOSE))

                val event = DDARegisterFixture.ddaEventOptOutApproved(document = ddaRegister.document)
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event.toJson())
                handler.onMessage(record)

                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.CLOSED
            }
        }

        context("com um evento de registro reprovado") {
            test("e o usuario nao é encontrado") {
                val message = DDARegisterFixture.ddaEventOptOutReproved(document = "************")
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                handler.onMessage(record)

                ddaRepository.findByDocument("************").shouldBeNull()
            }

            DDAStatus.values()
                .forEach { status ->
                    test("e o status for $status, o status não deve ser alterado") {
                        save(ddaRegister.copy(status = status))
                        val message = DDARegisterFixture.ddaEventOptOutReproved(document = ddaRegister.document)
                        val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                        handler.onMessage(record)

                        ddaRepository.find(ddaRegister.accountId)?.status shouldBe status
                    }
                }
        }

        context("com um evento de registro com erro") {
            test("e o usuario nao é encontrado") {
                val message = DDARegisterFixture.ddaEventOptOutError(document = "************")
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                handler.onMessage(record)

                ddaRepository.findByDocument("************").shouldBeNull()
            }

            test("informando que o usuário não é um pagador registrado") {
                save(ddaRegister.copy(status = DDAStatus.CLOSING))

                val message = DDARegisterFixture.ddaEventOptOutError(
                    document = ddaRegister.document,
                    errorCode = "PAYER_NOT_REGISTERED",
                    errorMessage = "Payer 74d3dbf5-e5b4-41e9-b7c5-da081410f0a7 is not eligible for opt-out."
                )
                handler.onMessage(ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson()))

                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.CLOSED
            }

            test("deve manter o status") {
                val message = DDARegisterFixture.ddaEventOptOutError(document = ddaRegister.document)
                val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", message.toJson())
                save(ddaRegister.copy(status = DDAStatus.CLOSING))
                handler.onMessage(record)
                ddaRepository.find(ddaRegister.accountId)?.status shouldBe DDAStatus.CLOSING
            }
        }
    }

    context("quando ocorrer um erro ao processar o evento") {
        test("deve enfileirar o evento para uma DLQ") {
            val event = DDARegisterFixture.ddaEventOptInApproved(document = ddaRegister.document)
            val record = ConsumerRecord("baas-dda-inbound", 1, 1, "", event)

            handler.handle(KafkaListenerException("test", null, null, record))
            verify { publisher.sendMessage("my-queue-name", record.value()) }
        }
    }
})

private fun PicPayResponseEventTO.toJson() = write(this)