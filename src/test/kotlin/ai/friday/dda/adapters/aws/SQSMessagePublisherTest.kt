package ai.friday.dda.adapters.aws

import ai.friday.dda.app.QueueMessage
import io.kotest.core.spec.style.FunSpec
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

internal class SQSMessagePublisherTest : FunSpec({

    test("deve reaproveitar resultado de queueURL de consultas anteriores") {
        val sqsClient: SqsClient = mockk(relaxed = true) {
            every { getQueueUrl(any<GetQueueUrlRequest>()).queueUrl() } returns "urlQueue1"
        }

        val queueMessage: QueueMessage = mockk(relaxed = true) {
            every { queueName } returns "queue1"
        }

        val sqsMessagePublisher = SQSMessagePublisher(sqsClient)

        for (i in 1..3) sqsMessagePublisher.sendMessage(queueMessage)

        verify(exactly = 1) { sqsClient.getQueueUrl(any<GetQueueUrlRequest>()) }
        verify(exactly = 3) { sqsClient.sendMessage(any<SendMessageRequest>()) }
    }
})