package ai.friday.dda.adapters.aws

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.lambda.LambdaClient
import software.amazon.awssdk.services.lambda.model.InvokeRequest
import software.amazon.awssdk.services.lambda.model.InvokeResponse
import software.amazon.awssdk.services.lambda.model.LambdaException

class LambdaInvokerTest : FunSpec({

    test("should successfully invoke lambda and return response") {
        // Given
        val lambdaClient: LambdaClient = mockk(relaxed = true)
        val invoker = LambdaInvoker(lambdaClient)

        val functionName = "test-function"
        val payload = mapOf("environment" to "FRIDAY")
        val expectedResponse = "lambda response"

        val mockResponse: InvokeResponse = mockk {
            every { statusCode() } returns 200
            every { payload() } returns SdkBytes.fromUtf8String(expectedResponse)
        }

        every { lambdaClient.invoke(any<InvokeRequest>()) } returns mockResponse

        // When
        val result = invoker.invoke(functionName, payload)

        // Then
        result.isSuccess shouldBe true
        result.getOrNull() shouldBe expectedResponse

        verify {
            lambdaClient.invoke(
                match<InvokeRequest> { request ->
                    request.functionName() == functionName &&
                        request.payload().asUtf8String().contains("FRIDAY")
                }
            )
        }
    }

    test("should handle lambda exception") {
        // Given
        val lambdaClient: LambdaClient = mockk(relaxed = true)
        val invoker = LambdaInvoker(lambdaClient)

        val functionName = "test-function"
        val payload = mapOf("environment" to "FRIDAY")

        every { lambdaClient.invoke(any<InvokeRequest>()) } throws LambdaException.builder()
            .message("Lambda service error")
            .build()

        // When
        val result = invoker.invoke(functionName, payload)

        // Then
        result.isFailure shouldBe true
    }

    test("should handle non-200 status code") {
        // Given
        val lambdaClient: LambdaClient = mockk(relaxed = true)
        val invoker = LambdaInvoker(lambdaClient)

        val functionName = "test-function"
        val payload = mapOf("environment" to "FRIDAY")

        val mockResponse: InvokeResponse = mockk {
            every { statusCode() } returns 500
        }

        every { lambdaClient.invoke(any<InvokeRequest>()) } returns mockResponse

        // When
        val result = invoker.invoke(functionName, payload)

        // Then
        result.isFailure shouldBe true
    }
})