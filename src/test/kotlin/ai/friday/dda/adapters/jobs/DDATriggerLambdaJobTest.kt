package ai.friday.dda.adapters.jobs

import ai.friday.dda.adapters.aws.LambdaInvoker
import ai.friday.dda.app.tenant.TenantConfiguration
import ai.friday.dda.app.tenant.TenantName
import io.kotest.core.spec.style.FunSpec
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class DDATriggerLambdaJobTest : FunSpec({

    val lambdaInvoker: LambdaInvoker = mockk(relaxed = true)
    val tenantConfiguration: TenantConfiguration = mockk(relaxed = true) {
        every { tenantName } returns TenantName("FRIDAY")
    }
    val lambdaFunctionName = "dda-files"

    val job = DDATriggerLambdaJob(
        tenantConfiguration,
        lambdaInvoker,
    )

    test("should invoke lambda with correct tenant environment") {
        // Given
        every { lambdaInvoker.invoke(any(), any()) } returns Result.success("success")

        // When
        job.execute()

        // Then
        verify {
            lambdaInvoker.invoke(
                lambdaFunctionName,
                mapOf("environment" to "FRIDAY")
            )
        }
    }
})