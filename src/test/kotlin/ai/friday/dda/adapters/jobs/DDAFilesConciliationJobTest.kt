package ai.friday.dda.adapters.jobs

import ai.friday.dda.app.brazilTimeZone
import ai.friday.dda.app.interfaces.ObjectRepository
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDateTime
import java.time.ZonedDateTime
import withGivenDateTime

class DDAFilesConciliationJobTest : DescribeSpec() {
    override fun isolationMode() = IsolationMode.InstancePerTest

    init {
        val objectRepositoryMock = mockk<ObjectRepository>()
        val ddaFilesConciliationJob = spyk(
            DDAFilesConciliationJob(
                objectRepository = objectRepositoryMock,
                configuration = mockk() {
                    every { bucket } returns "anything"
                }
            )
        ) {
            every { alarm(any()) } just runs
        }

        describe("quando não encontrar nenhum arquivo") {
            every { objectRepositoryMock.listObjectLastModified("anything", "processed/20230823") } returns emptyList()

            it("deve alarmar") {
                withGivenDateTime(ZonedDateTime.of(LocalDateTime.parse("2023-08-23T01:30:00"), brazilTimeZone)) {
                    ddaFilesConciliationJob.process()
                }
                verify { ddaFilesConciliationJob.alarm(any()) }
            }
        }

        describe("quando encontrar um arquivo com a maior data de criação") {
            every { objectRepositoryMock.listObjectLastModified("anything", "processed/20230823") } returns listOf(
                ZonedDateTime.of(LocalDateTime.parse("2023-08-23T09:00:00"), brazilTimeZone),
                ZonedDateTime.of(LocalDateTime.parse("2023-08-23T10:00:00"), brazilTimeZone)
            )

            describe("se essa hora for maior que 1h") {
                it("deve alarmar") {
                    withGivenDateTime(ZonedDateTime.of(LocalDateTime.parse("2023-08-23T11:30:00"), brazilTimeZone)) {
                        ddaFilesConciliationJob.process()
                    }
                    verify { ddaFilesConciliationJob.alarm(any()) }
                }
            }

            describe("se essa hora for menor que 1h") {
                it("não deve fazer nada") {
                    withGivenDateTime(ZonedDateTime.of(LocalDateTime.parse("2023-08-23T10:30:00"), brazilTimeZone)) {
                        ddaFilesConciliationJob.process()
                    }
                    verify(exactly = 0) { ddaFilesConciliationJob.alarm(any()) }
                }
            }
        }
    }
}