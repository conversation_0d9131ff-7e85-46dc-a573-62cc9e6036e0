package ai.friday.dda.adapters.jobs

import ai.friday.dda.PIC_PAY_ENV
import io.kotest.core.annotation.Ignored
import io.kotest.core.spec.style.FunSpec
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import utils.MicronautPropertiesTest

@MicronautTest(environments = [PIC_PAY_ENV])
@MicronautPropertiesTest
@Ignored
internal class MulticomBillsJobTest(
    private val multicomBillsJob: MulticomBillsJob,
    retrieverJob: MulticomBillsRetrieverJob
) : FunSpec({
    test("faz download dos arquivos e processa") {
        retrieverJob.run()
        multicomBillsJob.run()
    }
})