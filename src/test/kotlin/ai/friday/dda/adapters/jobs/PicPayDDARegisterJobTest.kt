package ai.friday.dda.adapters.jobs

import ai.friday.dda.DDARegisterFixture
import ai.friday.dda.DynamoDBUtils.putItem
import ai.friday.dda.DynamoDBUtils.setupDynamoDB
import ai.friday.dda.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.dda.adapters.dynamodb.DDADbRepository
import ai.friday.dda.adapters.dynamodb.DDADynamoDAO
import ai.friday.dda.adapters.dynamodb.toEntity
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptInEventTO
import ai.friday.dda.adapters.picpay.messaging.PicPayKafkaPublisher
import ai.friday.dda.app.account.AccountId
import ai.friday.dda.app.dda.DDAProvider
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDAStatus
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import java.time.ZonedDateTime
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

class PicPayDDARegisterJobTest : FunSpec({

    val dynamoDbEnhancedClient = setupDynamoDB()

    lateinit var ddaRepository: DDADbRepository
    lateinit var picPayKafkaPublisher: PicPayKafkaPublisher
    lateinit var picPayDdaRegisterJob: PicPayDDARegisterJob

    val executor =
        ThreadPoolExecutor(
            5,
            200,
            1,
            TimeUnit.MINUTES,
            LinkedBlockingQueue()
        )

    val ddaRegister =
        DDARegister(
            accountId = AccountId("**********"),
            document = "**********",
            created = ZonedDateTime.now(),
            status = DDAStatus.PENDING,
            lastUpdated = ZonedDateTime.now(),
            lastSuccessfullExecution = null,
            provider = DDAProvider.PICPAY
        )

    fun save(ddaRegister: DDARegister) {
        putItem(
            client = dynamoDbEnhancedClient,
            tableName = BILL_PAYMENT_TABLE_NAME,
            item =
            ddaRegister
                .toEntity()
        )
    }

    beforeTest {
        ddaRepository = spyk(DDADbRepository(DDADynamoDAO(dynamoDbEnhancedClient)))
        picPayKafkaPublisher = mockk()
        picPayDdaRegisterJob =
            PicPayDDARegisterJob(
                ddaRepository, picPayKafkaPublisher,
                executor
            )
    }

    context("quando nao existir usuario pendente de registro") {
        val pendingStatusList = listOf(DDAStatus.PENDING, DDAStatus.PENDING_MIGRATION_OPTIN)
        val testCaseStatusList = DDAStatus.values().toList().minus(pendingStatusList)

        testCaseStatusList.forEach { status ->
            test("nao deve atualizar registro quando o status for $status") {
                save(ddaRegister.copy(status = status))
                picPayDdaRegisterJob.execute()
                verify(exactly = 0) {
                    ddaRepository.save(any())
                    picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptInEventTO>())
                }
            }
        }
    }

    context("quando ha usuario pendente") {
        context("de opt-in ou migração com provider ARBI") {
            test("nao deve atualizar registro") {
                save(ddaRegister.copy(status = DDAStatus.PENDING, provider = DDAProvider.ARBI))
                picPayDdaRegisterJob.execute()
                verify(exactly = 0) {
                    ddaRepository.save(any())
                    picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptInEventTO>())
                }
            }
        }

        context("com provider PICPAY") {
            context("em processo de opt-in") {
                test("deve atualizar registro para REQUESTING e publicar mensagem no Kafka") {

                    every {
                        picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptInEventTO>())
                    } just runs

                    save(ddaRegister.copy(status = DDAStatus.PENDING, provider = DDAProvider.PICPAY))
                    picPayDdaRegisterJob.execute()
                    val register = ddaRepository.find(AccountId("**********"))
                    register.shouldNotBeNull()
                    register.status shouldBe DDAStatus.REQUESTING

                    verify {
                        picPayKafkaPublisher.send(
                            any(),
                            "friday",
                            withArg<PicPayDDAOptInEventTO> {
                                it.body.event shouldBe "DDA_OPT_IN_FROM_PICPAY_WAS_MADE"
                                it.body.source.personType shouldBe "NATURAL"
                                it.body.source.document shouldBe ddaRegister.document
                                it.body.source.accounts.first().agencyType shouldBe "PHYSICAL"
                                it.body.source.accounts.first().agencyNumber shouldBe "0001"
                                it.body.source.accounts.first().accountType shouldBe "CHECKING_ACCOUNT"
                                it.body.source.accounts.first().accountNumber shouldBe "*********"
                            }
                        )
                    }
                }
            }

            context("de migração") {
                test("deve atualizar registro para MIGRATING e publicar mensagem no Kafka") {

                    every {
                        picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptInEventTO>())
                    } just runs

                    save(ddaRegister.copy(status = DDAStatus.PENDING_MIGRATION_OPTIN, provider = DDAProvider.PICPAY))
                    picPayDdaRegisterJob.execute()
                    val register = ddaRepository.find(AccountId("**********"))
                    register.shouldNotBeNull()
                    register.status shouldBe DDAStatus.MIGRATING_OPTIN

                    verify {
                        picPayKafkaPublisher.send(
                            any(),
                            "friday",
                            withArg<PicPayDDAOptInEventTO> {
                                it.body.event shouldBe "DDA_OPT_IN_FROM_PICPAY_WAS_MADE"
                                it.body.source.personType shouldBe "NATURAL"
                                it.body.source.document shouldBe ddaRegister.document
                                it.body.source.accounts.first().agencyType shouldBe "PHYSICAL"
                                it.body.source.accounts.first().agencyNumber shouldBe "0001"
                                it.body.source.accounts.first().accountType shouldBe "CHECKING_ACCOUNT"
                                it.body.source.accounts.first().accountNumber shouldBe "*********"
                            }
                        )
                    }
                }
            }

            test("nao deve interromper o processamento do lote em casos de erro") {
                listOf(
                    DDARegisterFixture.createDDARegister(provider = DDAProvider.PICPAY),
                    DDARegisterFixture.createDDARegister("ACCOUNT-2", provider = DDAProvider.PICPAY),
                    DDARegisterFixture.createDDARegister(
                        "ACCOUNT-3",
                        provider = DDAProvider.PICPAY,
                        status = DDAStatus.PENDING_MIGRATION_OPTIN
                    ),
                    DDARegisterFixture.createDDARegister(
                        "ACCOUNT-4",
                        provider = DDAProvider.PICPAY,
                        status = DDAStatus.PENDING_MIGRATION_OPTIN
                    ),
                    DDARegisterFixture.createDDARegister("ACCOUNT-5", provider = DDAProvider.PICPAY)
                ).forEach(::save)

                // spec para cada execução de send para o publisher
                // primeira execução retorna Unit, na segunda Unit, terceira lança uma Exception, quarta Unit e quinta Unit
                every {
                    picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptInEventTO>())
                } returns Unit andThen Unit andThenThrows Exception() andThen Unit andThen Unit

                picPayDdaRegisterJob.execute()

                verify(exactly = 5) { picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptInEventTO>()) }
            }
        }
    }
})