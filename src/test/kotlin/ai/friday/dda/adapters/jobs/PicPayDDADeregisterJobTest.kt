package ai.friday.dda.adapters.jobs

import ai.friday.dda.DynamoDBUtils.putItem
import ai.friday.dda.DynamoDBUtils.setupDynamoDB
import ai.friday.dda.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.dda.adapters.dynamodb.DDADbRepository
import ai.friday.dda.adapters.dynamodb.DDADynamoDAO
import ai.friday.dda.adapters.dynamodb.toEntity
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptOutEventTO
import ai.friday.dda.adapters.picpay.messaging.PicPayKafkaPublisher
import ai.friday.dda.app.account.AccountId
import ai.friday.dda.app.dda.DDAProvider
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDAStatus
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import java.time.ZonedDateTime

class PicPayDDADeregisterJobTest : FunSpec({

    val dynamoDbEnhancedClient = setupDynamoDB()

    lateinit var ddaRepository: DDADbRepository
    lateinit var picPayKafkaPublisher: PicPayKafkaPublisher
    lateinit var picPayDdaDeregisterJob: PicPayDDADeregisterJob

    val ddaRegister = DDARegister(
        accountId = AccountId("**********"),
        document = "**********",
        created = ZonedDateTime.now(),
        status = DDAStatus.PENDING,
        lastUpdated = ZonedDateTime.now(),
        lastSuccessfullExecution = null,
        provider = DDAProvider.PICPAY
    )

    fun save(ddaRegister: DDARegister) {
        putItem(
            client = dynamoDbEnhancedClient,
            tableName = BILL_PAYMENT_TABLE_NAME,
            item = ddaRegister
                .toEntity()
        )
    }

    beforeTest {
        ddaRepository = spyk(DDADbRepository(DDADynamoDAO(dynamoDbEnhancedClient)))
        picPayKafkaPublisher = mockk()
        picPayDdaDeregisterJob = PicPayDDADeregisterJob(ddaRepository, picPayKafkaPublisher, "cron")
    }

    context("quando nao existir usuario pendente de remoção") {

        DDAStatus.values()
            .filter { it != DDAStatus.PENDING_CLOSE }
            .forEach { status ->
                test("nao deve atualizar registro quando o status for $status") {
                    save(ddaRegister.copy(status = status))
                    picPayDdaDeregisterJob.execute()
                    verify(exactly = 0) {
                        ddaRepository.save(any())
                        picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptOutEventTO>())
                    }
                }
            }
    }

    context("quando ha usuario pendente de remoção") {
        context("com provider ARBI") {
            test("nao deve atualizar registro") {
                save(ddaRegister.copy(status = DDAStatus.PENDING_CLOSE, provider = DDAProvider.ARBI))
                picPayDdaDeregisterJob.execute()
                verify(exactly = 0) {
                    ddaRepository.save(any())
                    picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptOutEventTO>())
                }
            }
        }

        context("com provider PICPAY") {
            context("em processo de encerramento de conta") {
                test("deve atualizar registro para CLOSING e publicar mensagem no Kafka") {

                    every {
                        picPayKafkaPublisher.send(any(), any(), any<PicPayDDAOptOutEventTO>())
                    } just runs

                    save(ddaRegister.copy(status = DDAStatus.PENDING_CLOSE, provider = DDAProvider.PICPAY))

                    picPayDdaDeregisterJob.execute()

                    val register = ddaRepository.find(AccountId("**********"))
                    register.shouldNotBeNull()
                    register.status shouldBe DDAStatus.CLOSING

                    verify {
                        picPayKafkaPublisher.send(
                            any(),
                            "friday",
                            withArg<PicPayDDAOptOutEventTO> {
                                it.body.event shouldBe "DDA_OPT_OUT_FROM_PICPAY_WAS_MADE"
                                it.body.source.personType shouldBe "NATURAL"
                                it.body.source.document shouldBe ddaRegister.document
                            }
                        )
                    }
                }
            }

            DDAStatus.values()
                .filter { it !in listOf(DDAStatus.PENDING_CLOSE, DDAStatus.CLOSING, DDAStatus.CLOSED) }
                .forEach { status ->
                    context("em processo de abertura de um outro account para o mesmo cpf: $status") {
                        test("não deve publicar mensagem no Kafka e deve atualizar o status para CLOSED") {

                            save(ddaRegister.copy(status = DDAStatus.PENDING_CLOSE, provider = DDAProvider.PICPAY))
                            save(
                                ddaRegister.copy(
                                    status = status,
                                    provider = DDAProvider.PICPAY,
                                    accountId = AccountId("*********")
                                )
                            )

                            picPayDdaDeregisterJob.execute()

                            val register = ddaRepository.find(AccountId("**********"))
                            register.shouldNotBeNull()
                            register.status shouldBe DDAStatus.CLOSED

                            verify {
                                picPayKafkaPublisher wasNot Called
                            }
                        }
                    }
                }
        }
    }
})