package ai.friday.dda.adapters.multicom

import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItem
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItemStatus
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoProvider
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.Document
import ai.friday.dda.app.bill.BarCode
import io.kotest.core.spec.style.AnnotationSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import withGivenDateTime

class MulticomAdapterTest : AnnotationSpec() {
    private val multicomAdapter = MulticomAdapter()
    private val csvStream = Thread.currentThread().contextClassLoader.getResourceAsStream("multicom/example.csv")
    private val csvStreamNoHeader =
        Thread.currentThread().contextClassLoader.getResourceAsStream("multicom/example_no_header.csv")

    private val now = BrazilZonedDateTimeSupplier.getZonedDateTime()

    private val concessionariaDiretoItem = ConcessionariaDiretoItem(
        amount = 3510,
        barCode = BarCode.ofDigitable("848000000006351001592029208221401995099190219237"),
        document = Document("12355183775"),
        dueDate = LocalDate.parse("2022-08-22"),
        description = "Plano Claro",
        provider = ConcessionariaDiretoProvider.MULTICOM,
        providerBillId = "23123",
        status = ConcessionariaDiretoItemStatus.ACTIVE,
        inactivityCounter = 0,
        lastSeen = now,
    )

    @Test
    fun `deve ler um arquivo CSV e retornar um multicomFileTO`() {
        withGivenDateTime(now) {
            val bills = multicomAdapter.processFile(csvStream!!)
            bills.size shouldBe 7
            bills[0] shouldBe concessionariaDiretoItem
            bills[0].providerBillId shouldBe "23123"
            bills[2].description shouldBe "Plano Claro"
            bills.forEach {
                it.status shouldBe ConcessionariaDiretoItemStatus.ACTIVE
            }
        }
    }

    @Test
    fun `deve ler um arquivo CSV sem header e retornar um multicomFileTO`() {
        withGivenDateTime(now) {
            val bills = multicomAdapter.processFile(csvStreamNoHeader!!)
            bills.size shouldBe 7
            bills[0] shouldBe concessionariaDiretoItem
        }
    }
}