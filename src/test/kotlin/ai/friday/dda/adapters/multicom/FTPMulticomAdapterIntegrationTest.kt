package ai.friday.dda.adapters.multicom

import ai.friday.dda.adapters.aws.S3ObjectRepository
import io.kotest.core.annotation.Ignored
import io.kotest.core.spec.style.AnnotationSpec
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client

@Ignored
internal class FTPMulticomAdapterIntegrationTest : AnnotationSpec() {

    private val multicomFTPConfiguration = MulticomFTPConfiguration(
        host = "edi01.ccsembratel.com.br",
        username = "PICPAY.MPAGOS.HML",
        key = "",
        passphrase = ""
    )
    private val ftpMulticomAdapter =
        FTPMulticomAdapter(
            multicomFTPConfiguration
        )
    private val s3ObjectRepository =
        S3ObjectRepository(s3Client = S3Client.builder().region(Region.of("us-east-1")).build())

    @Test
    fun `faz download do arquivo multicom e salva o arquivo na s3`() {
        val files = ftpMulticomAdapter.listFiles("/Outbound")
        val stream = ftpMulticomAdapter.downloadFile("/Outbound/${files[0]}")
        s3ObjectRepository.uploadObject(bucketName = "stg-multicom-files", key = files[0], stream = stream)
    }

    @Test
    fun `deleta um arquivo do ftp`() {
        val files = ftpMulticomAdapter.listFiles("/Outbound")
        ftpMulticomAdapter.deleteFile("/Outbound/${files[0]}")
    }
}