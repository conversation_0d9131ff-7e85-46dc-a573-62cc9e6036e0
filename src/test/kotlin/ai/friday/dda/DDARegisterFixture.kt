package ai.friday.dda

import ai.friday.dda.adapters.picpay.messaging.DDAOptInRequestAccount
import ai.friday.dda.adapters.picpay.messaging.DDAOptInRequestSource
import ai.friday.dda.adapters.picpay.messaging.DDAOptOutRequestSource
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventSourceOptInApproved
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventSourceOptInError
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventSourceOptInReproved
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventSourceOptOutApproved
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventSourceOptOutError
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAEventSourceOptOutReproved
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptInEventBody
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptInEventTO
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptOutEventBody
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAOptOutEventTO
import ai.friday.dda.adapters.picpay.messaging.PicPayDDARegisterResponseErrorSource
import ai.friday.dda.adapters.picpay.messaging.PicPayDDARegisterResponseSource
import ai.friday.dda.adapters.picpay.messaging.PicPayDDAResponseError
import ai.friday.dda.adapters.picpay.messaging.PicPayResponseEventTO
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.account.AccountId
import ai.friday.dda.app.dda.DDAProvider
import ai.friday.dda.app.dda.DDARegister
import ai.friday.dda.app.dda.DDAStatus
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper

object DDARegisterFixture {
    fun ddaEventOptInApproved(document: String) = PicPayResponseEventTO(
        body = PicPayDDAEventSourceOptInApproved(
            source = PicPayDDARegisterResponseSource(personType = "NATURAL", document = document)
        )
    )

    fun ddaEventOptInReproved(document: String) = PicPayResponseEventTO(
        body = PicPayDDAEventSourceOptInReproved(
            source = PicPayDDARegisterResponseSource(personType = "NATURAL", document = document)
        )
    )

    fun ddaEventOptInError(
        document: String,
        errorCode: String = "code",
        errorMessage: String = "message",
        payload: String? = null
    ) =
        PicPayResponseEventTO(
            body = PicPayDDAEventSourceOptInError(
                source = PicPayDDARegisterResponseErrorSource(
                    errors = listOf(PicPayDDAResponseError(code = errorCode, message = errorMessage)),
                    payload = payload
                        ?: jacksonObjectMapper().writeValueAsString(ddaEventOptInRequest(document = document))
                )
            )
        )

    fun ddaEventOptInErrorForOptOutRequest(
        document: String,
        errorCode: String = "code",
        errorMessage: String = "message"
    ) =
        ddaEventOptInError(
            document,
            errorCode,
            errorMessage,
            jacksonObjectMapper().writeValueAsString(ddaEventOptOutRequest(document = document))
        )

    private fun ddaEventOptInRequest(event: String = "DDA_OPT_IN_FROM_PICPAY_WAS_MADE", document: String) =
        PicPayDDAOptInEventTO(
            body = PicPayDDAOptInEventBody(
                event = event,
                source = DDAOptInRequestSource(
                    personType = "NATURAL",
                    document = document,
                    accounts = listOf(
                        DDAOptInRequestAccount(
                            agencyNumber = "agency_number",
                            accountNumber = "account_number"
                        )
                    )
                )
            )
        )

    private fun ddaEventOptOutRequest(event: String = "DDA_OPT_OUT_FROM_PICPAY_WAS_MADE", document: String) =
        PicPayDDAOptOutEventTO(
            body = PicPayDDAOptOutEventBody(
                source = DDAOptOutRequestSource(
                    personType = "NATURAL",
                    document = document
                )
            )
        )

    fun ddaEventOptOutApproved(document: String) = PicPayResponseEventTO(
        body = PicPayDDAEventSourceOptOutApproved(
            source = PicPayDDARegisterResponseSource(personType = "NATURAL", document = document)
        )
    )

    fun ddaEventOptOutReproved(document: String) = PicPayResponseEventTO(
        body = PicPayDDAEventSourceOptOutReproved(
            source = PicPayDDARegisterResponseSource(personType = "NATURAL", document = document)
        )
    )

    fun ddaEventOptOutError(document: String, errorCode: String = "code", errorMessage: String = "message") =
        PicPayResponseEventTO(
            body = PicPayDDAEventSourceOptOutError(
                source = PicPayDDARegisterResponseErrorSource(
                    errors = listOf(PicPayDDAResponseError(code = errorCode, message = errorMessage)),
                    payload = jacksonObjectMapper().writeValueAsString(ddaEventOptOutRequest(document = document))
                )
            )
        )

    fun createDDARegister(
        accountId: String = "ACCOUNT-1",
        document: String = "**********",
        status: DDAStatus = DDAStatus.PENDING,
        provider: DDAProvider = DDAProvider.ARBI
    ) = DDARegister(
        accountId = AccountId(accountId),
        document = document,
        created = BrazilZonedDateTimeSupplier.getZonedDateTime(),
        status = status,
        lastUpdated = BrazilZonedDateTimeSupplier.getZonedDateTime(),
        lastSuccessfullExecution = BrazilZonedDateTimeSupplier.getZonedDateTime(),
        provider = provider
    )
}