import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import java.time.ZonedDateTime

fun <T> withGivenDateTime(time: ZonedDateTime, toBeExecuted: () -> T): T {
    mockkObject(BrazilZonedDateTimeSupplier)
    every { getZonedDateTime() } returns time
    every { getLocalDate() } returns time.toLocalDate()
    val result = toBeExecuted()
    unmockkObject(BrazilZonedDateTimeSupplier)
    return result
}