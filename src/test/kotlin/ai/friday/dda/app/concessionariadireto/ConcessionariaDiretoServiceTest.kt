package ai.friday.dda.app.concessionariadireto

import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItem
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoItemStatus
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoProvider
import ai.friday.billpayment.app.concessionariadireto.ConcessionariaDiretoProviderService
import ai.friday.dda.DynamoDBUtils.setupDynamoDB
import ai.friday.dda.adapters.dynamodb.ConcessionariaDiretoDbRepository
import ai.friday.dda.adapters.dynamodb.ConcessionariaDiretoDynamoDAO
import ai.friday.dda.adapters.dynamodb.toEntity
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.Document
import ai.friday.dda.app.QueueMessageBatch
import ai.friday.dda.app.bill.BarCode
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.app.interfaces.MessagePublisherConfiguration
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.longs.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.mockk.Called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.io.InputStream
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import withGivenDateTime

internal class ConcessionariaDiretoServiceTest() :
    FunSpec({
        val dynamoDB = setupDynamoDB()
        val dynamoDBDAO = ConcessionariaDiretoDynamoDAO(dynamoDB)
        val concessionariaDiretoRepository = ConcessionariaDiretoDbRepository(dynamoDBDAO)
        val concessionariaDiretoProviderService = mockk<ConcessionariaDiretoProviderService>()
        val messagePublisher = mockk<MessagePublisher>(relaxed = true)
        val activeBillsConfiguration =
            MessagePublisherConfiguration(name = "queue", queueName = "concessionariaDireto", delay = 0)

        val inactiveBillsConfiguration =
            MessagePublisherConfiguration(name = "queue", queueName = "missingConcessionariaDireto", delay = 0)

        val multicomService = MulticomService(
            concessionariaDiretoProviderService = concessionariaDiretoProviderService,
            messagePublisher = messagePublisher,
            concessionariaDiretoRepository = concessionariaDiretoRepository,
            activeBillsPublisherConfiguration = activeBillsConfiguration,
            inactiveBillsPublisherConfiguration = inactiveBillsConfiguration
        )

        afterTest {
            clearMocks(messagePublisher)
        }

        val newItem = ConcessionariaDiretoItem(
            amount = 3510,
            barCode = BarCode.ofDigitable("848000000006351001592029208221401995099190219237"),
            document = Document("12355183775"),
            dueDate = LocalDate.parse("2022-08-22"),
            description = "CLARO",
            provider = ConcessionariaDiretoProvider.MULTICOM,
            providerBillId = "23123",
            status = ConcessionariaDiretoItemStatus.ACTIVE,
            inactivityCounter = 0,
            lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime(),
        )

        context("quando um item for novo e outro existir") {
            val firstDigitableLine = "848111111116351001592029208221401995099190219237"
            val secondDigitableLine = "848222222226351001592029208221401995099190219237"
            val firstExistingItem = newItem.copy(
                barCode = BarCode.ofDigitable(firstDigitableLine),
                dueDate = LocalDate.parse("2022-07-22"),
                lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime().minusDays(1)
            )
            val secondExistingItem = newItem.copy(
                barCode = BarCode.ofDigitable(secondDigitableLine),
                dueDate = LocalDate.parse("2022-07-22")
            )

            concessionariaDiretoRepository.save(
                firstExistingItem
            )

            every {
                concessionariaDiretoProviderService.processFile(any())
            } returns listOf(newItem, secondExistingItem)

            withGivenDateTime(BrazilZonedDateTimeSupplier.getZonedDateTime().minusSeconds(1)) {
                multicomService.processFile(InputStream.nullInputStream())
            }

            test("deve publicar somente uma mensagem na fila") {
                val slot = slot<QueueMessageBatch>()

                verify {
                    messagePublisher.sendMessageBatch(capture(slot))
                }

                val sentItem = with(slot.captured) {
                    queueName shouldBe activeBillsConfiguration.queueName
                    messages.size shouldBe 1
                    jacksonObjectMapper().readValue(messages[0], ConcessionariaDiretoTO::class.java)
                }

                with(sentItem) {
                    barcode shouldBe newItem.barCode.number
                    dueDate shouldBe newItem.dueDate.format(DateTimeFormatter.ISO_DATE)
                    document shouldBe newItem.document.value
                    amount shouldBe newItem.amount
                    description shouldBe newItem.description
                    provider shouldBe newItem.provider.name
                    providerBillId shouldBe newItem.providerBillId
                }
            }

            test("deve salvar o item novo") {
                val savedItem =
                    concessionariaDiretoRepository.find(
                        newItem.barCode,
                        newItem.dueDate
                    )

                savedItem shouldBe newItem
            }

            val itemUpdated =
                concessionariaDiretoRepository.find(firstExistingItem.barCode, firstExistingItem.dueDate)!!

            test("deve atualizar o lastSeen do item existente") {
                itemUpdated.lastSeen shouldBeAfter firstExistingItem.lastSeen
            }

            test("não deve atualizar o código de barras do item existente") {
                itemUpdated.barCode.digitable shouldBe firstExistingItem.barCode.digitable
            }

            test("não deve atualizar o contador de inatividade") {
                itemUpdated.inactivityCounter shouldBe firstExistingItem.inactivityCounter
            }
        }

        context("quando não forem encontradas contas no arquivo") {
            val firstDigitableLine = "848111111116351001592029208221401995099190219237"
            val firstExistingItem =
                newItem.copy(barCode = BarCode.ofDigitable(firstDigitableLine), dueDate = LocalDate.parse("2022-07-22"))

            concessionariaDiretoRepository.save(
                firstExistingItem
            )

            every {
                concessionariaDiretoProviderService.processFile(any())
            } returns emptyList()

            multicomService.processFile(InputStream.nullInputStream())

            val itemUpdated =
                concessionariaDiretoRepository.find(firstExistingItem.barCode, firstExistingItem.dueDate)!!

            test("nao deve publicar mensagens") {
                verify {
                    messagePublisher wasNot Called
                }
            }

            test("não deve atualizar o contador de inatividade") {
                itemUpdated.inactivityCounter shouldBe firstExistingItem.inactivityCounter
            }

            test("não deve atualizar o lastSeen do item existente") {
                itemUpdated.lastSeen shouldBe firstExistingItem.lastSeen
            }
        }

        context("quando um item existente não vier no último arquivo") {
            val firstDigitableLine = "848111111116351001592029208221401995099190219237"
            val firstExistingItem =
                newItem.copy(barCode = BarCode.ofDigitable(firstDigitableLine), dueDate = LocalDate.parse("2022-07-22"))

            concessionariaDiretoRepository.save(
                firstExistingItem
            )

            every {
                concessionariaDiretoProviderService.processFile(any())
            } returns listOf(newItem)

            multicomService.processFile(InputStream.nullInputStream())

            val itemUpdated =
                concessionariaDiretoRepository.find(firstExistingItem.barCode, firstExistingItem.dueDate)!!

            test("deve atualizar somente o contador de inatividade") {
                itemUpdated.inactivityCounter shouldBeGreaterThan firstExistingItem.inactivityCounter
            }

            test("não deve atualizar o lastSeen do item existente") {
                itemUpdated.lastSeen shouldBe firstExistingItem.lastSeen
            }
        }

        context("quando um item existente que não veio em um arquivo reaparece no último arquivo") {
            val firstDigitableLine = "848111111116351001592029208221401995099190219237"
            val firstExistingItem = newItem.copy(
                barCode = BarCode.ofDigitable(firstDigitableLine),
                dueDate = LocalDate.parse("2022-07-22"),
                inactivityCounter = 1
            )

            concessionariaDiretoRepository.save(
                firstExistingItem
            )

            every {
                concessionariaDiretoProviderService.processFile(any())
            } returns listOf(firstExistingItem)

            multicomService.processFile(InputStream.nullInputStream())

            val itemUpdated =
                concessionariaDiretoRepository.find(firstExistingItem.barCode, firstExistingItem.dueDate)!!

            test("deve zerar o contador de inatividade") {
                itemUpdated.inactivityCounter shouldBe 0
            }

            test("deve atualizar o lastSeen do item existente") {
                itemUpdated.lastSeen shouldBeAfter firstExistingItem.lastSeen
            }
        }

        context("checkExpiredBills") {
            test("deve publicar mensagem quando tem uma conta que não é atualizada a mais de 24 horas e não veio em pelo menos em 2 arquivos") {
                val concessionariaDiretoEntity = newItem.copy(inactivityCounter = 2).toEntity()

                concessionariaDiretoEntity.lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime().minusDays(1)
                    .format(DateTimeFormatter.ISO_DATE_TIME)
                concessionariaDiretoEntity.index1RangeKey = concessionariaDiretoEntity.lastSeen
                dynamoDBDAO.save(concessionariaDiretoEntity)

                multicomService.checkExpiredBills()

                verify {
                    messagePublisher.sendMessageBatch(
                        withArg {
                            it.messages.size shouldBe 1
                            it.queueName shouldBe inactiveBillsConfiguration.queueName
                            it.messages[0] shouldContain "\"amount\":3510"
                            it.messages[0] shouldContain "\"provider\":\"${ConcessionariaDiretoProvider.MULTICOM.name}\""
                        }
                    )
                }
            }

            test("não deve publicar mensagem quando tem uma conta que não é atualizada a mais de 24 horas e mas não veio somente em 1 arquivo") {
                val concessionariaDiretoEntity = newItem.copy(inactivityCounter = 1).toEntity()

                concessionariaDiretoEntity.lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime().minusDays(1)
                    .format(DateTimeFormatter.ISO_DATE_TIME)
                concessionariaDiretoEntity.index1RangeKey = concessionariaDiretoEntity.lastSeen
                dynamoDBDAO.save(concessionariaDiretoEntity)

                multicomService.checkExpiredBills()

                verify(exactly = 0) {
                    messagePublisher.sendMessageBatch(
                        any()
                    )
                }
            }

            test("não deve publicar mensagem quando tem uma conta atualizada a menos de 24 horas") {
                val concessionariaDiretoEntity = newItem.toEntity()

                concessionariaDiretoEntity.lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime().minusHours(23)
                    .format(DateTimeFormatter.ISO_DATE_TIME)
                concessionariaDiretoEntity.index1RangeKey = concessionariaDiretoEntity.lastSeen
                dynamoDBDAO.save(concessionariaDiretoEntity)

                multicomService.checkExpiredBills()

                verify(exactly = 0) {
                    messagePublisher.sendMessageBatch(
                        any()
                    )
                }
            }

            test("não deve publicar mensagem quando já inativou uma conta") {

                val concessionariaDiretoEntity = newItem.copy(inactivityCounter = 2).toEntity()

                concessionariaDiretoEntity.lastSeen = BrazilZonedDateTimeSupplier.getZonedDateTime().minusDays(1)
                    .format(DateTimeFormatter.ISO_DATE_TIME)
                concessionariaDiretoEntity.index1RangeKey = concessionariaDiretoEntity.lastSeen
                dynamoDBDAO.save(concessionariaDiretoEntity)

                multicomService.checkExpiredBills()

                verify {
                    messagePublisher.sendMessageBatch(
                        withArg {
                            it.messages.size shouldBe 1
                            it.queueName shouldBe inactiveBillsConfiguration.queueName
                            it.messages[0] shouldContain "\"amount\":3510"
                            it.messages[0] shouldContain "\"provider\":\"${ConcessionariaDiretoProvider.MULTICOM.name}\""
                        }
                    )
                }

                clearMocks(messagePublisher)

                multicomService.checkExpiredBills()
                verify(exactly = 0) {
                    messagePublisher.sendMessageBatch(
                        any()
                    )
                }
            }
        }
    })