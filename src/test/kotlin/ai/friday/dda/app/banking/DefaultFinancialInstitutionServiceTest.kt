package ai.friday.dda.app.banking

import ai.friday.dda.app.interfaces.FinancialIdentifier
import arrow.core.getOrHandle
import io.kotest.assertions.fail
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe

class DefaultFinancialInstitutionServiceTest : FunSpec({
    val institutionProvider = DefaultFinancialInstitutionProvider()
    val service = DefaultFinancialInstitutionService(institutionProvider)

    test("list institution provider") {
        val compeLookup = service.getInstitutionByCode(FinancialIdentifier.COMPE(1))

        val institutionCompe = compeLookup.getOrHandle { fail("") }
        institutionCompe.shouldNotBeNull().name.shouldBe("BCO DO BRASIL S.A.")

        val ispbLookup = service.getInstitutionByCode(FinancialIdentifier.ISPB(0))
        val institutionIspb = compeLookup.getOrHandle { fail("") }
        institutionIspb.shouldNotBeNull().name.shouldBe("BCO DO BRASIL S.A.")

        val twoDigitsCompe = service.getInstitutionByCode(FinancialIdentifier.COMPE(33))

        val institutionTwoDigits = twoDigitsCompe.getOrHandle { fail("") }
        institutionTwoDigits.shouldNotBeNull().name.shouldBe("BCO SANTANDER (BRASIL) S.A.")
    }
})