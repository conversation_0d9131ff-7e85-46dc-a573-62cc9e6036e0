package ai.friday.dda.app.dda

import ai.friday.dda.DDAEventFixture.billDataResponse
import ai.friday.dda.ServerError
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.interfaces.MessagePublisher
import arrow.core.getOrHandle
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeSameInstanceAs
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyAll
import java.time.Instant
import java.time.temporal.ChronoUnit
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.fail

internal class DDAFullImportServiceTest : FunSpec({

    lateinit var ddaService: DDAService
    lateinit var publisher: MessagePublisher

    lateinit var service: DDAFullImportService

    beforeEach {
        ddaService = mockk()
        publisher = mockk()

        service = DDAFullImportService(
            billsInternal = mockk(relaxed = true) {
                every {
                    queueName
                } returns "dda-bills-queue"
            },
            service = ddaService,
            publisher = publisher,
            activation = mockk(relaxed = true) {
                every {
                    queueName
                } returns "dda-activation-queue"
                every { delay } returns 900
            },
            configuration = FullImportConfiguration(10, 10)
        )
    }

    context("quando receber uma requisicao de full import") {
        context("devemos buscar os ddas no PicPay") {
            test("e em caso de erro devemos propagar o erro") {
                every { ddaService.findDDAByDocument(any()) } returns ServerError().left()

                val result = service.handleFullImport(
                    FullImportCommand(
                        document = "document",
                        createdAtInMillis = 1111111,
                        attempts = 10
                    )
                )
                if (result.isRight()) fail("should not happen")

                result.mapLeft { Assertions.assertTrue(it is ServerError) }

                verifyAll {
                    ddaService.findDDAByDocument("document")
                    publisher wasNot called
                }
            }

            test("e em caso de sucesso, devemos publicar as bills para fila de dda-bills e dda-activation quando estiver ativando o DDA do usuario") {
                val items = mutableListOf<BillRegisterData>()
                repeat(15) { items.add(mockk(relaxed = true, relaxUnitFun = true)) }

                every { ddaService.findDDAByDocument(any()) } returns billDataResponse(items).right()
                every { publisher.sendMessage(any(), any(), any()) } returns Unit.right()

                val result = service.handleFullImport(
                    FullImportCommand(
                        document = "document",
                        createdAtInMillis = 1111111,
                        attempts = 10,
                    )
                ).getOrHandle { fail("unexpected error") }

                result.shouldBeTypeOf<FullImportResult.Success>()

                verify { ddaService.findDDAByDocument("document") }
                verify {
                    publisher.sendMessage(
                        "dda-activation-queue",
                        withArg {
                            with(it as DDAActivationMessageTO) {
                                document shouldBe "document"
                                size shouldBe 15
                                provider shouldBe DDAProvider.PICPAY
                            }
                        },
                        900
                    )
                }
                verify(exactly = 15) { publisher.sendMessage("dda-bills-queue", any()) }
            }

            test("e em caso de sucesso, devemos publicar as bills para fila de dda-bills e nao publicar a dda-activation quando nao for um fluxo de ativacao de DDA") {
                val items = mutableListOf<BillRegisterData>()
                repeat(15) { items.add(mockk(relaxed = true, relaxUnitFun = true)) }

                every { ddaService.findDDAByDocument(any()) } returns billDataResponse(items).right()
                every { publisher.sendMessage(any(), any(), any()) } returns Unit.right()

                val result = service.handleFullImport(
                    FullImportCommand(
                        document = "document",
                        createdAtInMillis = 1111111,
                        attempts = 1,
                        activateDDA = false
                    )
                ).getOrHandle { fail("unexpected error") }

                result.shouldBeTypeOf<FullImportResult.Success>()

                verify { ddaService.findDDAByDocument("document") }
                verify(exactly = 0) {
                    publisher.sendMessage(
                        "dda-activation-queue",
                        any(),
                        any()
                    )
                }
                verify(exactly = 15) { publisher.sendMessage("dda-bills-queue", any()) }
            }

            context("e quando a listagem de títulos estiver vazia") {
                test("deve retornar RetryPolicyExceeded quando não puder retentar") {
                    val items = mutableListOf<BillRegisterData>()
                    every { ddaService.findDDAByDocument(any()) } returns billDataResponse(items).right()
                    every { publisher.sendMessage(any(), any(), any()) } returns Unit.right()

                    val result = service.handleFullImport(
                        FullImportCommand(
                            document = "document",
                            createdAtInMillis = Instant.now().minus(7, ChronoUnit.DAYS).toEpochMilli(),
                            attempts = 20
                        )
                    ).getOrHandle { fail("response of full import should be right") }

                    result.shouldBeSameInstanceAs(FullImportResult.RetryPolicyExceeded)

                    verify { publisher.sendMessage("dda-activation-queue", any(), 900) }
                    verify { ddaService.findDDAByDocument("document") }
                }

                test("deve retornar NoContent quando puder retentar") {
                    val items = mutableListOf<BillRegisterData>()
                    every { ddaService.findDDAByDocument(any()) } returns billDataResponse(items).right()

                    val result = service.handleFullImport(
                        FullImportCommand(
                            document = "document",
                            createdAtInMillis = Instant.now().toEpochMilli(),
                            attempts = 1
                        )
                    ).getOrHandle { fail("response of full import should be right") }

                    result.shouldBeSameInstanceAs(FullImportResult.NoContent)

                    verifyAll {
                        ddaService.findDDAByDocument("document")
                        publisher wasNot Called
                    }
                }
            }
        }
    }

    context("ao verificar se um comando de full import pode ser retentado") {
        test("e o número de tentativas estiver excedido o estabelecido, deve retornar false") {
            service.canRetry(
                FullImportCommand(
                    document = "document",
                    createdAtInMillis = Instant.now().toEpochMilli(),
                    attempts = 20
                )
            ).shouldBe(false)
        }

        test("e a idade da mensagem for maior que o limite configurado, deve retornar false") {
            service.canRetry(
                FullImportCommand(
                    document = "document",
                    createdAtInMillis = Instant.now().minus(11, ChronoUnit.HOURS).toEpochMilli(),
                    attempts = 1
                )
            ).shouldBe(false)
        }

        test("e não houverem mais retentativas ou tempo, deve retornar false") {
            service.canRetry(
                FullImportCommand(
                    document = "document",
                    createdAtInMillis = Instant.now().minus(11, ChronoUnit.HOURS).toEpochMilli(),
                    attempts = 11
                )
            ).shouldBe(false)
        }

        test("e houverem mais retentativas a serem realizadas e a mensagem estiver no limite de tempo desejável, deve retornar true") {
            service.canRetry(
                FullImportCommand(
                    document = "document",
                    createdAtInMillis = Instant.now().minus(9, ChronoUnit.HOURS).toEpochMilli(),
                    attempts = 9
                )
            ).shouldBe(true)
        }
    }
})