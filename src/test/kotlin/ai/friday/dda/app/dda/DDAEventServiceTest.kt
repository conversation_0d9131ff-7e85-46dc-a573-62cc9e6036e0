package ai.friday.dda.app.dda

import ai.friday.dda.ServerError
import ai.friday.dda.adapters.picpay.messaging.DDABillMessageTO
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.NotFoundErr
import ai.friday.dda.app.interfaces.DDAProviderServiceV2
import ai.friday.dda.app.interfaces.MessagePublisher
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyAll
import org.junit.jupiter.api.fail

internal class DDAEventServiceTest : FunSpec({
    val provider = mockk<DDAProviderServiceV2>()
    val publisher = mockk<MessagePublisher>()

    val service = DDAEventService(
        mockk(relaxed = true) {
            every {
                queueName
            } returns "queue"
        },
        provider,
        publisher
    )

    context("quando receber uma chamada ao serviço") {
        context("deve executar a chamada com o PicPay") {
            context("e a integracao com o PicPay for realizada") {
                test("com sucesso") {
                    every {
                        provider.findByCipId(any())
                    } returns mockk<BillRegisterData>(relaxed = true, relaxUnitFun = true).right()

                    every { publisher.sendMessage(any(), any(), any()) } returns Unit.right()

                    service.handleEvent("cip_id", "my_request_id", "UPDATE")

                    verifyAll {
                        provider.findByCipId(withArg { it.cipID shouldBe "cip_id" })

                        publisher.sendMessage(
                            "queue",
                            withArg<DDABillMessageTO> {
                                it.requestId shouldBe "my_request_id"
                                it.operationType shouldBe "UPDATE"
                            },
                            any()
                        )
                    }
                }

                listOf(ServerError(), NotFoundErr).forEach {
                    test("com erro $it devemos propagar o erro") {
                        every { provider.findByCipId(any()) } returns it.left()

                        val result = service.handleEvent("cip_id", "my_request_id", "NEW")
                        if (result.isRight()) fail("should not success")

                        result.mapLeft { err -> err shouldBe it }

                        verify {
                            provider.findByCipId(
                                withArg { filter ->
                                    filter.cipID shouldBe "cip_id"
                                }
                            )
                        }
                    }
                }
            }
        }
    }
})