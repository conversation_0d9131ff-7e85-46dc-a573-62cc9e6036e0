package ai.friday.dda.app.dda

import ai.friday.dda.DDAEventFixture.billDataResponse
import ai.friday.dda.app.BillRegisterData
import ai.friday.dda.app.Document
import ai.friday.dda.app.brazilTimeZone
import ai.friday.dda.app.interfaces.DDAProviderServiceV2
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyAll
import java.time.ZonedDateTime
import withGivenDateTime

internal class DDAServiceTest : FunSpec({
    val provider = mockk<DDAProviderServiceV2>()
    val service = DDAService(provider)

    test("deve realizar paginação para resultado maiores que 10") {
        val items = mutableListOf<BillRegisterData>()
        for (i in 1..15) items.add(mockk(relaxed = true, relaxUnitFun = true))

        every {
            provider.findByFilter(any())
        } returns billDataResponse(items, 0, 10).right() andThen billDataResponse(items, 11, 15).right()

        service.findDDAByDocument("document")

        verifyAll {
            provider.findByFilter(
                withArg {
                    it.page.pageNum shouldBe 1
                    it.document shouldBe Document("document")
                }
            )
            provider.findByFilter(withArg { it.page.pageNum shouldBe 2 })
        }
    }

    test("deve filtrar boletos 30 dias para tras e 10 anos a frente") {
        every { provider.findByFilter(any()) } returns billDataResponse(mutableListOf(), 0, 10).right()

        val datetime = ZonedDateTime.of(2023, 1, 13, 0, 0, 0, 0, brazilTimeZone)

        withGivenDateTime(datetime) { service.findDDAByDocument("document") }

        verify {
            provider.findByFilter(
                withArg {
                    it.page.pageNum shouldBe 1
                    it.document shouldBe Document("document")
                    it.dateRange.start shouldBe datetime.minusDays(30).toLocalDate()
                    it.dateRange.end shouldBe datetime.plusDays(365 * 10).toLocalDate()
                }
            )
        }
    }
})