package ai.friday.dda.app.dda

import ai.friday.dda.DDARegisterFixture.createDDARegister
import ai.friday.dda.ServerError
import ai.friday.dda.app.interfaces.DDARepository
import ai.friday.dda.app.interfaces.MessagePublisher
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.fail

class DDAOptInServiceTest : FunSpec({
    lateinit var ddaRepository: DDARepository
    lateinit var publisher: MessagePublisher

    lateinit var ddaOptInService: DDARegisterService

    beforeEach {
        ddaRepository = mockk(relaxed = true)
        publisher = mockk()

        ddaOptInService = DDARegisterService(
            mockk(relaxed = true) {
                every {
                    queueName
                } returns "queue"
            },
            repository = ddaRepository,
            publisher = publisher
        )
    }

    context("quando for uma aprovacao") {
        context("e nao encontrar o usuario pelo documento") {
            test("deve retornar USER_NOT_FOUND") {
                every { ddaRepository.findByDocument(any()) } returns null

                ddaOptInService.handleOptInApproved("123").fold(
                    { it shouldBe DDARegisterResponseError.RegisterNotFound },
                    { fail("deveria retornar erro") }
                )
            }
        }

        context("e o usuario estiver na base") {
            val statusesOptin = DDARegisterService.OptinValidStatuses
            val statusesMigration = DDARegisterService.MigrationValidStatuses

            val statuses = statusesMigration + statusesOptin + DDAStatus.DENIED

            DDAStatus.values().filter { it !in statuses }.forEach { status ->
                context("com status $status") {
                    test("deve retornar INVALID_STATUS") {
                        every { ddaRepository.findByDocument(any()) } returns createDDARegister(status = status)

                        ddaOptInService.handleOptInApproved("123").fold(
                            { it shouldBe DDARegisterResponseError.InvalidStatus },
                            { fail("deveria retornar erro") }
                        )
                    }
                }
            }

            val testCases = listOf(
                Pair(DDAStatus.REQUESTED, statusesOptin),
                Pair(DDAStatus.PENDING_MIGRATION_OPTOUT, statusesMigration),
                Pair(DDAStatus.REQUESTED, listOf(DDAStatus.DENIED)),
            )

            testCases.forEach { (expectedStatus, ddaStatuses) ->
                ddaStatuses.forEach { ddaStatus ->
                    context("com status $ddaStatus") {
                        test("deve atualizar o status para $expectedStatus quando publicacao da mensagem for sucesso") {
                            every { ddaRepository.findByDocument(any()) } returns createDDARegister(status = ddaStatus)
                            every { publisher.sendMessage(any(), any(), 0) } returns Unit.right()

                            ddaOptInService.handleOptInApproved("123").fold(
                                { fail("deveria retornar erro") },
                                {
                                    it.shouldNotBeNull()

                                    verify {
                                        ddaRepository.save(withArg { ddaRegisterUpdated -> ddaRegisterUpdated.status shouldBe expectedStatus })
                                        publisher.sendMessage("queue", mapOf("document" to "123"), 0)
                                    }
                                }
                            )
                        }

                        test("nao deve atualizar status para $expectedStatus quando publicacao da mensagem for erro") {
                            every { ddaRepository.findByDocument(any()) } returns createDDARegister(status = ddaStatus)
                            every { publisher.sendMessage(any(), any(), any()) } returns ServerError().left()

                            val result = ddaOptInService.handleOptInApproved("123")
                            if (result.isRight()) fail("should not happen")

                            result.mapLeft { Assertions.assertTrue(it is ServerError) }

                            verify(exactly = 0) { ddaRepository.save(any()) }
                            verify {
                                publisher.sendMessage("queue", mapOf("document" to "123"), 0)
                            }
                        }
                    }
                }
            }
        }
    }

    context("quando for reprovado") {
        context("e nao encontrar usuario pelo documento") {
            test("deve retornar USER_NOT_FOUND") {
                every { ddaRepository.findByDocument(any()) } returns null

                ddaOptInService.handleOptInReproved("123").fold(
                    { it shouldBe DDARegisterResponseError.RegisterNotFound },
                    { fail("deveria retornar erro") }
                )
            }
        }

        context("e o usuario estiver na base") {
            val statuses = DDARegisterService.DenyableStatuses

            DDAStatus.values().filter { it !in statuses }.forEach { status ->
                context("com status $status") {
                    test("deve retornar INVALID_STATUS") {
                        every { ddaRepository.findByDocument(any()) } returns createDDARegister(status = status)

                        ddaOptInService.handleOptInReproved("123").fold(
                            { it shouldBe DDARegisterResponseError.InvalidStatus },
                            { fail("deveria retornar erro") }
                        )
                    }
                }
            }

            statuses.forEach { status ->
                context("com status $status") {
                    test("deve atualizar o status para DENIED") {
                        every { ddaRepository.findByDocument(any()) } returns createDDARegister(status = status)

                        ddaOptInService.handleOptInReproved("123").fold(
                            { fail("nao deveria retornar erro") },
                            {
                                it.shouldNotBeNull()
                                verify {
                                    ddaRepository.save(withArg { ddaRegisterUpdated -> ddaRegisterUpdated.status shouldBe DDAStatus.DENIED })
                                }
                            }
                        )
                    }
                }
            }
        }
    }

    // FIXME
    context("quando for erro") {
        context("e nao encontrar usuario pelo documento") {
            test("deve retornar USER_NOT_FOUND") {
                every { ddaRepository.findByDocument(any()) } returns null
            }
        }

        context("e o usuario estiver na base") {
            val statuses = listOf(DDAStatus.REQUESTING)

            DDAStatus.values().filter { it !in statuses }.forEach { status ->
                context("com status $status") {
                    test("deve retornar INVALID_STATUS") {
                        every { ddaRepository.findByDocument(any()) } returns createDDARegister(status = status)
                    }
                }
            }

            statuses.forEach { status ->
                context("com status $status") {
                    test("deve atualizar o status para PENDING") {
                        every { ddaRepository.findByDocument(any()) } returns createDDARegister(status = status)
                    }
                }
            }
        }
    }
})