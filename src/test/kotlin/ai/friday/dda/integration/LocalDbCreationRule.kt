package ai.friday.dda.integration

import com.amazonaws.services.dynamodbv2.local.embedded.DynamoDBEmbedded
import com.amazonaws.services.dynamodbv2.local.main.ServerRunner
import com.amazonaws.services.dynamodbv2.local.shared.access.AmazonDynamoDBLocal

class LocalDbCreationRule {

    companion object {
        @JvmStatic
        lateinit var dynamoDB: AmazonDynamoDBLocal

        @JvmStatic
        fun startServer() {
            if (!this::dynamoDB.isInitialized) {
                System.setProperty("sqlite4java.library.path", "native-libs")
                val port = "8000"
                val server = ServerRunner.createServerFromCommandLineArgs(arrayOf("-inMemory", "-port", port))
                server!!.start()
                dynamoDB = DynamoDBEmbedded.create()
            }
        }
    }
}